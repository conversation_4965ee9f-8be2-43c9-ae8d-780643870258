// Performance Monitoring and Optimization Utilities

interface PerformanceMetrics {
  loadTime: number
  domContentLoaded: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
  timeToInteractive: number
}

interface ResourceTiming {
  name: string
  duration: number
  size: number
  type: string
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private observers: PerformanceObserver[] = []
  private resourceTimings: ResourceTiming[] = []

  constructor() {
    this.initializeObservers()
    this.measureBasicMetrics()
  }

  private initializeObservers() {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1] as any
          this.metrics.largestContentfulPaint = lastEntry.startTime
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)
      } catch (e) {
        console.warn('LCP observer not supported')
      }

      // First Input Delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            this.metrics.firstInputDelay = entry.processingStart - entry.startTime
          })
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)
      } catch (e) {
        console.warn('FID observer not supported')
      }

      // Cumulative Layout Shift
      try {
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          this.metrics.cumulativeLayoutShift = clsValue
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)
      } catch (e) {
        console.warn('CLS observer not supported')
      }

      // Resource Timing
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            this.resourceTimings.push({
              name: entry.name,
              duration: entry.duration,
              size: entry.transferSize || 0,
              type: this.getResourceType(entry.name)
            })
          })
        })
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)
      } catch (e) {
        console.warn('Resource observer not supported')
      }
    }
  }

  private measureBasicMetrics() {
    // Wait for page load
    if (document.readyState === 'complete') {
      this.collectBasicMetrics()
    } else {
      window.addEventListener('load', () => {
        this.collectBasicMetrics()
      })
    }
  }

  private collectBasicMetrics() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    if (navigation) {
      this.metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart
      this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart
    }

    // First Contentful Paint
    const paintEntries = performance.getEntriesByType('paint')
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    if (fcpEntry) {
      this.metrics.firstContentfulPaint = fcpEntry.startTime
    }

    // Time to Interactive (approximation)
    this.estimateTimeToInteractive()
  }

  private estimateTimeToInteractive() {
    // Simple TTI estimation based on main thread quiet periods
    const longTasks = performance.getEntriesByType('longtask')
    if (longTasks.length === 0) {
      this.metrics.timeToInteractive = this.metrics.domContentLoaded || 0
    } else {
      const lastLongTask = longTasks[longTasks.length - 1]
      this.metrics.timeToInteractive = lastLongTask.startTime + lastLongTask.duration
    }
  }

  private getResourceType(url: string): string {
    if (url.includes('.css')) return 'stylesheet'
    if (url.includes('.js')) return 'script'
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'image'
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font'
    return 'other'
  }

  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics }
  }

  public getResourceTimings(): ResourceTiming[] {
    return [...this.resourceTimings]
  }

  public getPerformanceScore(): number {
    const weights = {
      fcp: 0.15,
      lcp: 0.25,
      fid: 0.25,
      cls: 0.25,
      tti: 0.10
    }

    let score = 100
    
    // First Contentful Paint (good: <1.8s, poor: >3.0s)
    if (this.metrics.firstContentfulPaint) {
      const fcp = this.metrics.firstContentfulPaint / 1000
      if (fcp > 3.0) score -= weights.fcp * 40
      else if (fcp > 1.8) score -= weights.fcp * 20
    }

    // Largest Contentful Paint (good: <2.5s, poor: >4.0s)
    if (this.metrics.largestContentfulPaint) {
      const lcp = this.metrics.largestContentfulPaint / 1000
      if (lcp > 4.0) score -= weights.lcp * 40
      else if (lcp > 2.5) score -= weights.lcp * 20
    }

    // First Input Delay (good: <100ms, poor: >300ms)
    if (this.metrics.firstInputDelay) {
      const fid = this.metrics.firstInputDelay
      if (fid > 300) score -= weights.fid * 40
      else if (fid > 100) score -= weights.fid * 20
    }

    // Cumulative Layout Shift (good: <0.1, poor: >0.25)
    if (this.metrics.cumulativeLayoutShift) {
      const cls = this.metrics.cumulativeLayoutShift
      if (cls > 0.25) score -= weights.cls * 40
      else if (cls > 0.1) score -= weights.cls * 20
    }

    // Time to Interactive (good: <3.8s, poor: >7.3s)
    if (this.metrics.timeToInteractive) {
      const tti = this.metrics.timeToInteractive / 1000
      if (tti > 7.3) score -= weights.tti * 40
      else if (tti > 3.8) score -= weights.tti * 20
    }

    return Math.max(0, Math.round(score))
  }

  public generateReport(): string {
    const metrics = this.getMetrics()
    const score = this.getPerformanceScore()
    const resources = this.getResourceTimings()

    let report = `Performance Report (Score: ${score}/100)\n`
    report += `=====================================\n\n`

    report += `Core Web Vitals:\n`
    report += `- First Contentful Paint: ${metrics.firstContentfulPaint ? (metrics.firstContentfulPaint / 1000).toFixed(2) + 's' : 'N/A'}\n`
    report += `- Largest Contentful Paint: ${metrics.largestContentfulPaint ? (metrics.largestContentfulPaint / 1000).toFixed(2) + 's' : 'N/A'}\n`
    report += `- First Input Delay: ${metrics.firstInputDelay ? metrics.firstInputDelay.toFixed(2) + 'ms' : 'N/A'}\n`
    report += `- Cumulative Layout Shift: ${metrics.cumulativeLayoutShift ? metrics.cumulativeLayoutShift.toFixed(3) : 'N/A'}\n\n`

    report += `Other Metrics:\n`
    report += `- DOM Content Loaded: ${metrics.domContentLoaded ? (metrics.domContentLoaded / 1000).toFixed(2) + 's' : 'N/A'}\n`
    report += `- Load Time: ${metrics.loadTime ? (metrics.loadTime / 1000).toFixed(2) + 's' : 'N/A'}\n`
    report += `- Time to Interactive: ${metrics.timeToInteractive ? (metrics.timeToInteractive / 1000).toFixed(2) + 's' : 'N/A'}\n\n`

    // Resource breakdown
    const resourcesByType = resources.reduce((acc, resource) => {
      if (!acc[resource.type]) {
        acc[resource.type] = { count: 0, totalSize: 0, totalDuration: 0 }
      }
      acc[resource.type].count++
      acc[resource.type].totalSize += resource.size
      acc[resource.type].totalDuration += resource.duration
      return acc
    }, {} as Record<string, { count: number; totalSize: number; totalDuration: number }>)

    report += `Resource Summary:\n`
    Object.entries(resourcesByType).forEach(([type, stats]) => {
      report += `- ${type}: ${stats.count} files, ${(stats.totalSize / 1024).toFixed(1)}KB, ${stats.totalDuration.toFixed(0)}ms\n`
    })

    return report
  }

  public cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Utility functions for performance optimization
export const performanceUtils = {
  // Debounce function for performance
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate = false
  ): (...args: Parameters<T>) => void {
    let timeout: number | null = null
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        timeout = null
        if (!immediate) func(...args)
      }
      const callNow = immediate && !timeout
      if (timeout) clearTimeout(timeout)
      timeout = window.setTimeout(later, wait)
      if (callNow) func(...args)
    }
  },

  // Throttle function for performance
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean
    return function executedFunction(...args: Parameters<T>) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  // Intersection Observer for lazy loading
  createIntersectionObserver(
    callback: (entries: IntersectionObserverEntry[]) => void,
    options: IntersectionObserverInit = {}
  ): IntersectionObserver {
    const defaultOptions = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    }
    return new IntersectionObserver(callback, defaultOptions)
  },

  // Preload critical resources
  preloadResource(href: string, as: string, type?: string): void {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    if (type) link.type = type
    document.head.appendChild(link)
  },

  // Prefetch resources for next navigation
  prefetchResource(href: string): void {
    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = href
    document.head.appendChild(link)
  },

  // Measure function execution time
  measureExecutionTime<T>(
    name: string,
    func: () => T
  ): T {
    const start = performance.now()
    const result = func()
    const end = performance.now()
    console.log(`${name} took ${(end - start).toFixed(2)}ms`)
    return result
  },

  // Check if user prefers reduced motion
  prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  },

  // Check connection quality
  getConnectionInfo(): { effectiveType?: string; downlink?: number; rtt?: number } {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      }
    }
    return {}
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// Auto-cleanup on page unload
window.addEventListener('beforeunload', () => {
  performanceMonitor.cleanup()
})

export default PerformanceMonitor
