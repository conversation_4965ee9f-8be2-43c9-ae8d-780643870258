<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { ref, computed } from 'vue'
import {
  Document,
  Tools,
  Operation,
  VideoCamera,
  Reading,
  Expand,
  Fold,
  House,
  Menu,
  Setting,
  Moon,
  Sunny,
  Close
} from '@element-plus/icons-vue'

const route = useRoute()
const isCollapse = ref(false)
const isDarkMode = ref(false)
const isMobileMenuOpen = ref(false)
const isMobileSidebarOpen = ref(false)

const handleMenuCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  // 这里可以添加主题切换逻辑
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const toggleMobileSidebar = () => {
  isMobileSidebarOpen.value = !isMobileSidebarOpen.value
}

const closeMobileSidebar = () => {
  isMobileSidebarOpen.value = false
}

const pageTitle = computed(() => {
  const routeMap: Record<string, string> = {
    '/': '首页',
    '/text/dedupe': '文本去重',
    '/text/sort': '文本排序',
    '/text/count': '字数统计',
    '/text/encode': '编码解码',
    '/text/regex': '正则测试',
    '/text/markdown': 'Markdown转换',
    '/text/diff': '文本对比',
    '/text/format': '文本格式化',
    '/text/lorem': 'Lorem生成器',
    '/dev/json': 'JSON工具',
    '/dev/hash': '哈希加密',
    '/dev/qr': '二维码生成',
    '/dev/cron': 'Cron表达式',
    '/dev/network': '网络工具',
    '/file/pdf': 'PDF工具',
    '/file/office': 'Office转换',
    '/file/image': '图片处理',
    '/file/icon': '图标生成',
    '/calc/unit': '单位换算',
    '/calc/currency': '汇率换算',
    '/calc/health': '健康计算',
    '/calc/finance': '财务计算',
    '/media/image-editor': '图片编辑',
    '/media/gif-tools': 'GIF工具',
    '/media/video-tools': '视频工具',
    '/media/audio-tools': '音频工具',
    '/edu/mind-map': '思维导图',
    '/edu/latex-editor': 'LaTeX编辑器',
    '/edu/scientific-calculator': '科学计算器',
    '/edu/language-tools': '语言工具',
    '/edu/study-assistant': '学习辅助'
  }
  return routeMap[route.path] || '工具箱'
})
</script>

<template>
  <el-container class="layout-container">
    <!-- 移动端导航切换按钮 -->
    <button class="mobile-nav-toggle show-mobile" @click="toggleMobileSidebar">
      <el-icon><Menu /></el-icon>
    </button>

    <!-- 侧边栏 -->
    <el-aside
      :width="isCollapse ? '72px' : '280px'"
      :class="['sidebar', {
        'mobile-open': isMobileMenuOpen,
        'sidebar-collapsed': isCollapse,
        'mobile-sidebar-open': isMobileSidebarOpen
      }]"
    >
      <div class="logo-container">
        <div class="logo-content">
          <div class="logo-icon-wrapper">
            <div class="icon-container icon-container-primary icon-lg">
              <el-icon><Tools /></el-icon>
            </div>
          </div>
          <div v-if="!isCollapse" class="logo-text-wrapper">
            <h2 class="logo-text">ToolBox</h2>
            <span class="logo-subtitle">专业工具集合</span>
          </div>
        </div>
        <button class="mobile-close-btn show-mobile" @click="closeMobileSidebar">
          <el-icon><Close /></el-icon>
        </button>
      </div>

      <el-menu
        :default-active="route.path"
        class="sidebar-menu"
        :collapse="isCollapse"
        router
        :unique-opened="true"
      >
        <el-menu-item index="/" class="menu-item-home">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </el-menu-item>

        <el-sub-menu index="text">
          <template #title>
            <el-icon><Document /></el-icon>
            <span>文本工具</span>
          </template>
          <el-menu-item index="/text/dedupe">文本去重</el-menu-item>
          <el-menu-item index="/text/sort">文本排序</el-menu-item>
          <el-menu-item index="/text/count">字数统计</el-menu-item>
          <el-menu-item index="/text/encode">编码解码</el-menu-item>
          <el-menu-item index="/text/regex">正则测试</el-menu-item>
          <el-menu-item index="/text/markdown">Markdown转换</el-menu-item>
          <el-menu-item index="/text/diff">文本对比</el-menu-item>
          <el-menu-item index="/text/generator">随机生成</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="dev">
          <template #title>
            <el-icon><Operation /></el-icon>
            <span>开发工具</span>
          </template>
          <el-menu-item index="/dev/format">代码格式化</el-menu-item>
          <el-menu-item index="/dev/code">代码工具</el-menu-item>
          <el-menu-item index="/dev/time">时间转换</el-menu-item>
          <el-menu-item index="/dev/hash">加密解密</el-menu-item>
          <el-menu-item index="/dev/http">HTTP请求</el-menu-item>
          <el-menu-item index="/dev/qrcode">二维码工具</el-menu-item>
          <el-menu-item index="/dev/cron">Cron表达式</el-menu-item>
          <el-menu-item index="/dev/network">网络工具</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="file">
          <template #title>
            <el-icon><Document /></el-icon>
            <span>文件工具</span>
          </template>
          <el-menu-item index="/file/pdf">PDF工具</el-menu-item>
          <el-menu-item index="/file/office">Office转换</el-menu-item>
          <el-menu-item index="/file/image">图片处理</el-menu-item>
          <el-menu-item index="/file/icon">图标生成</el-menu-item>
        </el-sub-menu>

        <!-- 数据计算工具 -->
        <el-sub-menu index="4">
          <template #title>
            <el-icon><Operation /></el-icon>
            <span>数据计算</span>
          </template>
          <el-menu-item index="/calc/unit">单位换算器</el-menu-item>
          <el-menu-item index="/calc/currency">汇率换算器</el-menu-item>
          <el-menu-item index="/calc/health">健康计算器</el-menu-item>
          <el-menu-item index="/calc/finance">财务计算器</el-menu-item>
        </el-sub-menu>

        <!-- 多媒体处理工具 -->
        <el-sub-menu index="5">
          <template #title>
            <el-icon><VideoCamera /></el-icon>
            <span>多媒体处理</span>
          </template>
          <el-menu-item index="/media/image-editor">图片编辑器</el-menu-item>
          <el-menu-item index="/media/gif-tools">GIF 工具</el-menu-item>
          <el-menu-item index="/media/video-tools">视频工具</el-menu-item>
          <el-menu-item index="/media/audio-tools">音频工具</el-menu-item>
        </el-sub-menu>

        <!-- 教育学习工具 -->
        <el-sub-menu index="6">
          <template #title>
            <el-icon><Reading /></el-icon>
            <span>教育学习</span>
          </template>
          <el-menu-item index="/edu/mindmap">思维导图</el-menu-item>
          <el-menu-item index="/edu/latex">LaTeX 编辑器</el-menu-item>
          <el-menu-item index="/edu/calculator">科学计算器</el-menu-item>
          <el-menu-item index="/edu/language">语言工具</el-menu-item>
          <el-menu-item index="/edu/study">学习辅助</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container class="main-container">
      <!-- 顶部导航 -->
      <!-- <el-header class="header">
        <div class="header-left">
          <el-button
            text
            @click="handleMenuCollapse"
            class="collapse-btn"
          >
            <el-icon size="18">
              <Expand v-if="isCollapse" />
              <Fold v-else />
            </el-icon>
          </el-button>

          <!-- 移动端菜单按钮 -->
          <el-button
            text
            @click="toggleMobileMenu"
            class="mobile-menu-btn"
          >
            <el-icon size="18"><Menu /></el-icon>
          </el-button>

          <h1 class="page-title">{{ pageTitle }}</h1>
        </div>

        <div class="header-right">
          <el-tooltip content="切换主题" placement="bottom">
            <el-button
              text
              @click="toggleDarkMode"
              class="header-action-btn theme-toggle"
            >
              <el-icon size="20">
                <Moon v-if="!isDarkMode" />
                <Sunny v-else />
              </el-icon>
            </el-button>
          </el-tooltip>

          <el-tooltip content="设置" placement="bottom">
            <el-button
              text
              class="header-action-btn settings-btn"
            >
              <el-icon size="20"><Setting /></el-icon>
            </el-button>
          </el-tooltip>

          <div class="user-avatar">
            <el-avatar :size="36" class="avatar">
              <el-icon size="18"><Tools /></el-icon>
            </el-avatar>
          </div>
        </div>
      </el-header> -->

      <!-- 主要内容 -->
      <el-main class="main-content" :class="{ 'sidebar-collapsed': isCollapse }">
        <div class="content-area">
          <RouterView class="fade-in" />
        </div>
      </el-main>
    </el-container>

    <!-- 移动端遮罩 -->
    <div
      v-if="isMobileMenuOpen"
      class="mobile-overlay"
      @click="toggleMobileMenu"
    ></div>
  </el-container>
</template>

<style scoped>
/* Enhanced Logo Styles */
.logo-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
  z-index: 1;
  padding: var(--spacing-sm);
}

.logo-icon-wrapper {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.logo-icon-wrapper:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.logo-icon {
  color: var(--neutral-white);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text-wrapper {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-text {
  color: var(--neutral-white);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  line-height: 1.2;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  letter-spacing: -0.025em;
}

.logo-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.05em;
  opacity: 0.9;
}

/* Enhanced Header Styles */
.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-action-btn {
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text-secondary) !important;
  border-radius: var(--radius-lg) !important;
  width: 44px !important;
  height: 44px !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
  backdrop-filter: blur(10px) !important;
  position: relative !important;
  overflow: hidden !important;
}

.header-action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: var(--gradient-primary);
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  transform: translate(-50%, -50%);
  opacity: 0.1;
  z-index: -1;
}

.header-action-btn:hover {
  background: var(--color-surface-hover) !important;
  border-color: var(--primary-200) !important;
  color: var(--primary-600) !important;
  transform: translateY(-2px) scale(1.05) !important;
  box-shadow: var(--shadow-lg) !important;
}

.header-action-btn:hover::before {
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

/* User Avatar */
.user-avatar {
  position: relative;
}

.avatar {
  background: var(--gradient-primary) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: var(--shadow-colored) !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
  cursor: pointer !important;
}

.avatar:hover {
  transform: scale(1.1) !important;
  box-shadow: var(--shadow-glow) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

/* Page Title Enhancement */
.page-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-heading);
  margin: 0;
  letter-spacing: -0.025em;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced Sidebar Styles */
.sidebar {
  background: var(--color-background-elevated);
  border-right: 1px solid var(--color-border);
  box-shadow: var(--shadow-lg);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  backdrop-filter: blur(20px);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) transparent;
}

.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-full);
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.3;
}

.sidebar.sidebar-collapsed {
  box-shadow: var(--shadow-xl);
}

.sidebar.sidebar-collapsed .logo-text-wrapper,
.sidebar.sidebar-collapsed .menu-item-text {
  opacity: 0;
  transform: translateX(-10px);
}

/* Layout Container */
.layout-container {
  height: 100vh;
  overflow: hidden;
}

/* Enhanced Main Container */
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--color-background-soft);
}

/* Enhanced Header */
.header {
  background: var(--color-background-elevated);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(20px);
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-200), transparent);
  opacity: 0.5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.collapse-btn {
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text-secondary) !important;
  border-radius: var(--radius-lg) !important;
  width: 44px !important;
  height: 44px !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

.collapse-btn:hover {
  background: var(--color-surface-hover) !important;
  border-color: var(--color-border-hover) !important;
  color: var(--color-text-primary) !important;
  transform: scale(1.05) !important;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text-secondary) !important;
  border-radius: var(--radius-lg) !important;
  width: 44px !important;
  height: 44px !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

.mobile-menu-btn:hover {
  background: var(--color-surface-hover) !important;
  border-color: var(--color-border-hover) !important;
  color: var(--color-text-primary) !important;
  transform: scale(1.05) !important;
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  z-index: 999;
  display: none;
  animation: fadeIn 0.3s ease;
}

/* Content Area */
.content-area {
  padding: var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.main-content {
  background: var(--color-background-soft);
  flex: 1;
  overflow-y: auto;
  position: relative;
  margin-left: 280px;
  transition: margin-left 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.main-content.sidebar-collapsed {
  margin-left: 72px;
}

.main-content::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .content-area {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .mobile-menu-btn {
    display: inline-flex;
  }

  .collapse-btn {
    display: none;
  }

  .mobile-overlay {
    display: block;
  }

  .page-title {
    font-size: var(--font-size-lg);
  }

  .header-right {
    gap: var(--spacing-sm);
  }

  .header-action-btn {
    width: 40px !important;
    height: 40px !important;
  }

  .avatar {
    width: 32px !important;
    height: 32px !important;
  }

  .content-area {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: var(--font-size-lg);
  }

  .page-title {
    font-size: var(--font-size-base);
  }

  .header-action-btn {
    width: 36px !important;
    height: 36px !important;
  }

  .avatar {
    width: 28px !important;
    height: 28px !important;
  }

  .content-area {
    padding: var(--spacing-sm);
  }

  .logo-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .header-left {
    gap: var(--spacing-md);
  }

  .sidebar {
    width: 100vw !important;
  }
}

/* Animation Enhancements */
.sidebar {
  animation: slideRight 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.header {
  animation: slideDown 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.fade-in {
  animation: fadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Menu Item Enhancements */
.sidebar :deep(.el-menu-item),
.sidebar :deep(.el-sub-menu__title) {
  border-radius: var(--radius-lg) !important;
  margin: 4px 12px !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
  font-weight: var(--font-weight-medium) !important;
  position: relative !important;
  overflow: hidden !important;
}

.sidebar :deep(.el-menu-item::before),
.sidebar :deep(.el-sub-menu__title::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  transition: left 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  opacity: 0.1;
  z-index: -1;
}

.sidebar :deep(.el-menu-item:hover::before),
.sidebar :deep(.el-sub-menu__title:hover::before) {
  left: 0;
}

.sidebar :deep(.el-menu-item:hover),
.sidebar :deep(.el-sub-menu__title:hover) {
  background: var(--color-surface-hover) !important;
  color: var(--primary-600) !important;
  transform: translateX(8px) !important;
  box-shadow: var(--shadow-sm) !important;
}

.sidebar :deep(.el-menu-item.is-active) {
  background: var(--primary-50) !important;
  color: var(--primary-600) !important;
  font-weight: var(--font-weight-semibold) !important;
  transform: translateX(8px) !important;
  box-shadow: var(--shadow-md) !important;
  border-left: 4px solid var(--primary-500) !important;
}

.sidebar :deep(.el-sub-menu .el-menu-item) {
  margin-left: 24px !important;
  font-size: var(--font-size-sm) !important;
}

/* 移动端导航按钮 */
.mobile-close-btn {
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  margin-left: auto;
}

.mobile-close-btn:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-secondary);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }

  .sidebar {
    left: -280px;
    width: 280px !important;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border-right: 1px solid var(--color-border);
  }

  .sidebar.mobile-sidebar-open {
    left: 0;
    box-shadow: var(--shadow-2xl);
  }

  .main-content,
  .main-content.sidebar-collapsed {
    margin-left: 0;
    width: 100%;
    padding-top: var(--spacing-16);
  }

  .logo-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
  }

  .mobile-nav-toggle {
    position: fixed;
    top: var(--spacing-4);
    left: var(--spacing-4);
    z-index: 999;
    background: var(--color-background-elevated);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    color: var(--color-text-primary);
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .mobile-nav-toggle:hover {
    background: var(--color-surface-hover);
    transform: scale(1.05);
    box-shadow: var(--shadow-xl);
  }

  .mobile-nav-toggle:active {
    transform: scale(0.95);
  }

  /* 移动端遮罩层 */
  .sidebar.mobile-sidebar-open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 280px;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(2px);
    z-index: -1;
  }
}

/* Dark mode specific enhancements */
@media (prefers-color-scheme: dark) {
  .logo-icon-wrapper {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
  }

  .header-action-btn {
    backdrop-filter: blur(20px) !important;
  }

  .sidebar,
  .header {
    backdrop-filter: blur(30px) !important;
  }

  @media (max-width: 768px) {
    .sidebar {
      background: rgba(0, 0, 0, 0.95);
    }

    .mobile-nav-toggle {
      background: var(--color-background-elevated);
      border-color: var(--color-border);
    }
  }
}

</style>
