<template>
  <div 
    ref="containerRef" 
    class="lazy-loader-container"
    :class="{ 'lazy-loading': isLoading, 'lazy-loaded': isLoaded, 'lazy-error': hasError }"
  >
    <!-- Loading State -->
    <div v-if="isLoading && !isLoaded" class="lazy-loading-state">
      <SkeletonLoader v-if="showSkeleton" :type="skeletonType" :count="skeletonCount" />
      <div v-else class="lazy-spinner-container">
        <div class="lazy-spinner"></div>
        <p v-if="loadingText" class="lazy-loading-text">{{ loadingText }}</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="hasError" class="lazy-error-state">
      <div class="lazy-error-icon">
        <el-icon><Warning /></el-icon>
      </div>
      <h3 class="lazy-error-title">{{ errorTitle || '加载失败' }}</h3>
      <p class="lazy-error-message">{{ errorMessage || '内容加载时出现错误，请稍后重试' }}</p>
      <button v-if="showRetry" @click="retry" class="lazy-retry-btn">
        <el-icon><Refresh /></el-icon>
        重试
      </button>
    </div>

    <!-- Loaded Content -->
    <div v-else-if="isLoaded" class="lazy-content" :class="contentClass">
      <slot></slot>
    </div>

    <!-- Intersection Observer Target -->
    <div v-if="useIntersectionObserver && !isLoaded && !hasError" 
         ref="observerTarget" 
         class="lazy-observer-target">
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { Warning, Refresh } from '@element-plus/icons-vue'
import SkeletonLoader from './SkeletonLoader.vue'

interface Props {
  // Loading behavior
  loadOnMount?: boolean
  useIntersectionObserver?: boolean
  threshold?: number
  rootMargin?: string
  
  // Loading function
  loadFunction?: () => Promise<any>
  
  // UI customization
  showSkeleton?: boolean
  skeletonType?: 'card' | 'list' | 'table' | 'form' | 'chart' | 'gallery' | 'text'
  skeletonCount?: number
  loadingText?: string
  
  // Error handling
  errorTitle?: string
  errorMessage?: string
  showRetry?: boolean
  maxRetries?: number
  
  // Styling
  containerClass?: string
  contentClass?: string
  
  // Performance
  debounceMs?: number
  cacheKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  loadOnMount: false,
  useIntersectionObserver: true,
  threshold: 0.1,
  rootMargin: '50px',
  showSkeleton: true,
  skeletonType: 'card',
  skeletonCount: 3,
  showRetry: true,
  maxRetries: 3,
  debounceMs: 300,
  containerClass: '',
  contentClass: ''
})

const emit = defineEmits<{
  loaded: [data: any]
  error: [error: Error]
  retry: [attempt: number]
}>()

// Reactive state
const containerRef = ref<HTMLElement>()
const observerTarget = ref<HTMLElement>()
const isLoading = ref(false)
const isLoaded = ref(false)
const hasError = ref(false)
const retryCount = ref(0)
const loadedData = ref<any>(null)

// Intersection Observer
let intersectionObserver: IntersectionObserver | null = null
let debounceTimer: number | null = null

// Cache for loaded content
const cache = new Map<string, any>()

const load = async () => {
  if (isLoading.value || isLoaded.value) return

  // Check cache first
  if (props.cacheKey && cache.has(props.cacheKey)) {
    loadedData.value = cache.get(props.cacheKey)
    isLoaded.value = true
    emit('loaded', loadedData.value)
    return
  }

  isLoading.value = true
  hasError.value = false

  try {
    if (props.loadFunction) {
      const data = await props.loadFunction()
      loadedData.value = data
      
      // Cache the result
      if (props.cacheKey) {
        cache.set(props.cacheKey, data)
      }
      
      isLoaded.value = true
      emit('loaded', data)
    } else {
      // If no load function provided, just mark as loaded
      await nextTick()
      isLoaded.value = true
      emit('loaded', null)
    }
  } catch (error) {
    hasError.value = true
    emit('error', error as Error)
  } finally {
    isLoading.value = false
  }
}

const debouncedLoad = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  debounceTimer = window.setTimeout(() => {
    load()
  }, props.debounceMs)
}

const retry = async () => {
  if (retryCount.value >= props.maxRetries) {
    return
  }
  
  retryCount.value++
  hasError.value = false
  emit('retry', retryCount.value)
  
  // Add exponential backoff
  const delay = Math.min(1000 * Math.pow(2, retryCount.value - 1), 5000)
  await new Promise(resolve => setTimeout(resolve, delay))
  
  await load()
}

const setupIntersectionObserver = () => {
  if (!props.useIntersectionObserver || !observerTarget.value) return

  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !isLoaded.value && !isLoading.value) {
          debouncedLoad()
        }
      })
    },
    {
      threshold: props.threshold,
      rootMargin: props.rootMargin
    }
  )

  intersectionObserver.observe(observerTarget.value)
}

const cleanup = () => {
  if (intersectionObserver) {
    intersectionObserver.disconnect()
    intersectionObserver = null
  }
  
  if (debounceTimer) {
    clearTimeout(debounceTimer)
    debounceTimer = null
  }
}

// Watchers
watch(() => props.loadFunction, () => {
  // Reset state when load function changes
  isLoaded.value = false
  hasError.value = false
  retryCount.value = 0
  
  if (props.loadOnMount) {
    debouncedLoad()
  }
})

// Lifecycle
onMounted(async () => {
  await nextTick()
  
  if (props.loadOnMount) {
    debouncedLoad()
  } else if (props.useIntersectionObserver) {
    setupIntersectionObserver()
  }
})

onUnmounted(() => {
  cleanup()
})

// Expose methods for parent components
defineExpose({
  load: debouncedLoad,
  retry,
  reset: () => {
    isLoaded.value = false
    hasError.value = false
    retryCount.value = 0
    loadedData.value = null
  },
  isLoading: () => isLoading.value,
  isLoaded: () => isLoaded.value,
  hasError: () => hasError.value,
  getData: () => loadedData.value
})
</script>

<style scoped>
.lazy-loader-container {
  width: 100%;
  min-height: 100px;
  position: relative;
}

.lazy-loading-state {
  width: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.lazy-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.lazy-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.lazy-loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

.lazy-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  text-align: center;
  background: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  min-height: 200px;
}

.lazy-error-icon {
  width: 48px;
  height: 48px;
  background: var(--error-100);
  color: var(--error-600);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-4);
  font-size: 24px;
}

.lazy-error-title {
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-2) 0;
}

.lazy-error-message {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0 0 var(--spacing-4) 0;
  max-width: 300px;
  line-height: 1.5;
}

.lazy-retry-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  background: var(--primary-500);
  color: var(--neutral-white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.lazy-retry-btn:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.lazy-retry-btn:active {
  transform: translateY(0);
}

.lazy-content {
  animation: fadeInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.lazy-observer-target {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  pointer-events: none;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .lazy-error-state {
    padding: var(--spacing-6);
    min-height: 150px;
  }
  
  .lazy-error-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .lazy-error-title {
    font-size: var(--font-size-base);
  }
  
  .lazy-error-message {
    font-size: var(--font-size-xs);
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .lazy-error-icon {
    background: var(--error-900);
    color: var(--error-400);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .lazy-spinner {
    animation: none;
  }
  
  .lazy-content {
    animation: none;
  }
  
  .lazy-retry-btn {
    transition: none;
  }
  
  .lazy-retry-btn:hover {
    transform: none;
  }
}
</style>
