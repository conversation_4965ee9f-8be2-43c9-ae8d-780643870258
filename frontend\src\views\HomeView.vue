<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import La<PERSON><PERSON>oader from '@/components/LazyLoader.vue'
import SkeletonLoader from '@/components/SkeletonLoader.vue'
import {
  Operation,
  Money,
  Odometer,
  Picture,
  VideoPlay,
  VideoCamera,
  Microphone,
  Share,
  EditPen,
  DataAnalysis,
  ChatDotRound,
  Clock,
  Folder,
  Tools,
  ArrowRight,
  Star,
  TrendCharts
} from '@element-plus/icons-vue'

const router = useRouter()

const heroStats = ref([
  { number: '26+', label: '实用工具', icon: 'Tools' },
  { number: '6', label: '工具分类', icon: 'Operation' },
  { number: '100%', label: '免费使用', icon: 'Share' },
  { number: '0', label: '数据上传', icon: 'Share' }
])

// 图标组件映射
const iconComponents: Record<string, any> = {
  Operation,
  Money,
  Odometer,
  Picture,
  VideoPlay,
  VideoCamera,
  Microphone,
  Share,
  EditPen,
  DataAnalysis,
  ChatDotRound,
  Clock,
  Folder,
  Tools,
  ArrowRight,
  Star,
  TrendCharts,
  Document: EditPen,
  Monitor: DataAnalysis
}

const toolCategories = ref([
  {
    title: '文本工具',
    icon: 'Document',
    description: '文本处理、格式化、编码解码等工具',
    tools: [
      { name: '文本去重', path: '/text/dedupe', desc: '去除重复的文本行' },
      { name: '文本排序', path: '/text/sort', desc: '按字典序或长度排序文本' },
      { name: '字数统计', path: '/text/count', desc: '统计字符数、单词数、行数' },
      { name: '编码解码', path: '/text/encode', desc: 'URL、Base64等编码解码' },
      { name: '正则测试', path: '/text/regex', desc: '正则表达式测试和匹配' },
      { name: 'Markdown转换', path: '/text/markdown', desc: 'Markdown与HTML互转' },
      { name: '文本对比', path: '/text/diff', desc: '逐行对比文本差异' },
      { name: '随机生成', path: '/text/generator', desc: '生成随机文本和密码' }
    ]
  },
  {
    title: '开发工具',
    icon: 'Monitor',
    description: '开发者常用的代码处理和转换工具',
    tools: [
      { name: '代码格式化', path: '/dev/format', desc: '格式化JSON、XML、CSS等代码' },
      { name: '代码工具', path: '/dev/code', desc: '语法高亮、转换、混淆等' },
      { name: '时间转换', path: '/dev/time', desc: '时间戳转换和时区转换' },
      { name: '加密解密', path: '/dev/hash', desc: 'MD5、SHA、AES、RSA加密' },
      { name: 'HTTP请求', path: '/dev/http', desc: '类似Postman的HTTP测试工具' },
      { name: '二维码工具', path: '/dev/qrcode', desc: '生成和解析二维码' },
      { name: 'Cron表达式', path: '/dev/cron', desc: '可视化生成cron表达式' },
      { name: '网络工具', path: '/dev/network', desc: 'IP查询、域名解析、Ping测试' }
    ]
  },
  {
    title: '文件工具',
    icon: 'Folder',
    description: '文件处理、格式转换、图片编辑等工具',
    tools: [
      { name: 'PDF工具', path: '/file/pdf', desc: 'PDF合并、拆分、转换、加密' },
      { name: 'Office转换', path: '/file/office', desc: 'Word、Excel、PPT格式转换' },
      { name: '图片处理', path: '/file/image', desc: '图片压缩、格式转换、水印' },
      { name: '图标生成', path: '/file/icon', desc: 'Favicon、ICO图标生成' }
    ]
  }
])

const calcTools = ref([
  { name: '单位换算器', path: '/calc/unit', desc: '长度、重量、面积、体积等单位转换', icon: 'Operation' },
  { name: '汇率换算器', path: '/calc/currency', desc: '实时汇率查询和历史图表', icon: 'Money' },
  { name: '健康计算器', path: '/calc/health', desc: 'BMI、基础代谢率、卡路里计算', icon: 'Odometer' },
  { name: '财务计算器', path: '/calc/finance', desc: '贷款、投资、复利、税费计算', icon: 'Money' }
])

const mediaTools = ref([
  { name: '图片编辑器', path: '/media/image-editor', desc: '在线裁剪、旋转、滤镜、调色等', icon: 'Picture' },
  { name: 'GIF 工具', path: '/media/gif-tools', desc: 'GIF 压缩、帧提取、多图合成', icon: 'VideoPlay' },
  { name: '视频工具', path: '/media/video-tools', desc: '视频转换、压缩、截取、水印', icon: 'VideoCamera' },
  { name: '音频工具', path: '/media/audio-tools', desc: '音频剪辑、格式转换、降噪', icon: 'Microphone' }
])

const eduTools = ref([
  { name: '思维导图', path: '/edu/mindmap', desc: '拖拽式节点编辑，多种模板导出', icon: 'Share' },
  { name: 'LaTeX 编辑器', path: '/edu/latex', desc: '数学公式实时预览，模板库', icon: 'EditPen' },
  { name: '科学计算器', path: '/edu/calculator', desc: '复杂运算、矩阵计算、函数绘图', icon: 'DataAnalysis' },
  { name: '语言工具', path: '/edu/language', desc: '汉字转拼音、词典、批量翻译', icon: 'ChatDotRound' },
  { name: '学习辅助', path: '/edu/study', desc: '番茄钟、记忆卡片、进度追踪', icon: 'Clock' }
])

const navigateToTool = (path: string) => {
  router.push(path)
}

const showCategoryTools = (category: any) => {
  // 这里可以添加展开分类工具的逻辑
  console.log('Show tools for:', category.title)
  // 或者导航到第一个工具
  if (category.tools.length > 0) {
    navigateToTool(category.tools[0].path)
  }
}

const getCategoryThemeClass = (categoryTitle: string) => {
  const themeMap: Record<string, string> = {
    '文本工具': 'category-card-text',
    '开发工具': 'category-card-dev',
    '文件工具': 'category-card-file',
    '数据计算': 'category-card-calc',
    '多媒体处理': 'category-card-media',
    '教育学习': 'category-card-edu'
  }
  return themeMap[categoryTitle] || 'category-card-default'
}
</script>

<template>
  <div class="home-container">
    <!-- Enhanced Hero Section -->
    <div class="hero-section">
      <div class="hero-background">
        <div class="hero-shape hero-shape-1"></div>
        <div class="hero-shape hero-shape-2"></div>
        <div class="hero-shape hero-shape-3"></div>
      </div>

      <div class="hero-content">
        <div class="hero-badge">
          <el-icon class="badge-icon" size="16"><Operation /></el-icon>
          <span>专业工具平台</span>
        </div>

        <h1 class="hero-title">
          <span class="title-main">现代化工具箱</span>
          <span class="title-sub">一站式数字化解决方案</span>
        </h1>

        <p class="hero-subtitle">
          集成26+专业工具，覆盖文本处理、开发辅助、多媒体编辑等多个领域<br>
          <strong>全部免费，本地处理，保护隐私</strong>
        </p>

        <div class="hero-actions">
          <el-button type="primary" size="large" class="hero-btn-primary" @click="navigateToTool('/text/dedupe')">
            <el-icon><Tools /></el-icon>
            开始使用
          </el-button>
          <el-button size="large" class="hero-btn-secondary">
            <el-icon><VideoPlay /></el-icon>
            观看演示
          </el-button>
        </div>

        <div class="hero-stats">
          <div class="stat-item" v-for="(stat, index) in heroStats" :key="index">
            <div class="stat-icon">
              <el-icon :size="24"><component :is="iconComponents[stat.icon]" /></el-icon>
            </div>
            <div class="stat-content">
              <span class="stat-number">{{ stat.number }}</span>
              <span class="stat-label">{{ stat.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Categories Section -->
    <div class="categories-section">
      <div class="section-header">
        <h2 class="section-title">工具分类</h2>
        <p class="section-subtitle">选择您需要的工具类别，快速找到解决方案</p>
      </div>

      <div class="categories-grid">
        <div
          v-for="(category, index) in toolCategories"
          :key="category.title"
          class="category-card-wrapper"
          :class="`stagger-${index + 1}`"
        >
          <div
            class="category-card"
            :class="getCategoryThemeClass(category.title)"
            @click="showCategoryTools(category)"
          >
            <div class="category-icon-wrapper">
              <el-icon class="category-icon" size="32">
                <component :is="iconComponents[category.icon]" />
              </el-icon>
              <div class="icon-glow"></div>
            </div>

            <div class="category-content">
              <h3 class="category-title">{{ category.title }}</h3>
              <p class="category-description">{{ category.description }}</p>

              <div class="category-tools-preview">
                <el-tag
                  v-for="(tool, idx) in category.tools.slice(0, 3)"
                  :key="idx"
                  size="small"
                  class="tool-tag"
                >
                  {{ tool.name }}
                </el-tag>
                <span v-if="category.tools.length > 3" class="more-tools">
                  +{{ category.tools.length - 3 }} 更多
                </span>
              </div>

              <div class="category-footer">
                <span class="tool-count">{{ category.tools.length }} 个工具</span>
                <el-icon class="arrow-icon" size="16"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据计算工具 -->
    <div class="tools-section">
      <h2 class="section-title">数据计算工具</h2>
      <LazyLoader
        :use-intersection-observer="true"
        :show-skeleton="true"
        skeleton-type="gallery"
        :skeleton-count="4"
        :load-on-mount="false"
      >
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :md="6" v-for="tool in calcTools" :key="tool.name">
            <el-card class="tool-card theme-calc" shadow="never">
              <div class="tool-header">
                <el-icon class="tool-icon" :size="32">
                  <component :is="iconComponents[tool.icon]" />
                </el-icon>
                <h3 class="tool-title">{{ tool.name }}</h3>
              </div>
              <p class="tool-description">{{ tool.desc }}</p>
              <div class="tool-actions">
                <el-button
                  class="tool-btn"
                  @click="navigateToTool(tool.path)"
                >
                  使用工具
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </LazyLoader>
    </div>

    <!-- 多媒体处理工具 -->
    <div class="tools-section">
      <h2 class="section-title">多媒体处理工具</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="tool in mediaTools" :key="tool.name">
          <el-card class="tool-card theme-media" shadow="hover">
            <div class="tool-header">
              <el-icon class="tool-icon" :size="32">
                <component :is="iconComponents[tool.icon]" />
              </el-icon>
              <h3 class="tool-title">{{ tool.name }}</h3>
            </div>
            <p class="tool-description">{{ tool.desc }}</p>
            <div class="tool-actions">
              <el-button
                class="tool-btn"
                @click="navigateToTool(tool.path)"
              >
                使用工具
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 教育学习工具 -->
    <div class="tools-section">
      <h2 class="section-title">教育学习工具</h2>
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="6" v-for="tool in eduTools" :key="tool.name">
          <el-card class="tool-card theme-edu" shadow="never">
            <div class="tool-header">
              <el-icon class="tool-icon" :size="32">
                <component :is="iconComponents[tool.icon]" />
              </el-icon>
              <h3 class="tool-title">{{ tool.name }}</h3>
            </div>
            <p class="tool-description">{{ tool.desc }}</p>
            <div class="tool-actions">
              <el-button
                class="tool-btn"
                @click="navigateToTool(tool.path)"
              >
                使用工具
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 特性介绍 -->
    <div class="features-section">
      <h2 class="section-title">平台特性</h2>
      <el-row :gutter="24">
        <el-col :xs="24" :sm="8" :md="8">
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="32"><Operation /></el-icon>
            </div>
            <h3>高效便捷</h3>
            <p>无需安装，打开即用，所有工具都经过优化，响应迅速</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8">
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="32"><Share /></el-icon>
            </div>
            <h3>安全可靠</h3>
            <p>所有处理都在本地进行，不会上传您的数据，保护隐私安全</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8">
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="32"><DataAnalysis /></el-icon>
            </div>
            <h3>功能丰富</h3>
            <p>涵盖文本处理、开发工具、文件转换等多个领域的实用工具</p>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  position: relative;
}

.home-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Enhanced Hero Section - Soft Theme */
.hero-section {
  position: relative;
  padding: calc(var(--spacing-3xl) * 2) var(--spacing-xl);
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 0.98) 50%,
    rgba(226, 232, 240, 0.95) 100%);
  border-radius: var(--radius-4xl);
  margin-bottom: calc(var(--spacing-3xl) * 1.5);
  overflow: hidden;
  text-align: center;
  color: var(--color-text-primary);
  box-shadow: var(--shadow-xl);
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(20px);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.hero-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(139, 92, 246, 0.06));
  animation: float 8s ease-in-out infinite;
  filter: blur(1px);
}

.hero-shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.06), rgba(16, 185, 129, 0.04));
}

.hero-shape-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 15%;
  animation-delay: -2s;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.03));
}

.hero-shape-3 {
  width: 100px;
  height: 100px;
  top: 60%;
  right: 20%;
  animation-delay: -4s;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.04), rgba(245, 158, 11, 0.03));
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.5;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--primary-50);
  backdrop-filter: blur(10px);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-full);
  padding: var(--spacing-sm) var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.05em;
  color: var(--primary-600);
  animation: slideDown 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.2s both;
  box-shadow: var(--shadow-sm);
}

.badge-icon {
  animation: rotate 2s ease-in-out infinite;
}

@keyframes rotate {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(5deg); }
  75% { transform: rotate(-5deg); }
}

.hero-title {
  margin-bottom: var(--spacing-xl);
  animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.4s both;
}

.title-main {
  display: block;
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-black);
  line-height: 1.1;
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--color-text-primary) 0%, var(--secondary-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.08));
  letter-spacing: -0.02em;
}

.title-sub {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  letter-spacing: 0.025em;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
  margin-bottom: calc(var(--spacing-xl) * 1.5);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.6s both;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: calc(var(--spacing-xl) * 2);
  animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.8s both;
}

.hero-btn-primary {
  background: var(--gradient-primary) !important;
  border: 1px solid var(--primary-500) !important;
  color: var(--neutral-white) !important;
  font-weight: var(--font-weight-semibold) !important;
  padding: var(--spacing-lg) calc(var(--spacing-xl) * 1.5) !important;
  border-radius: var(--radius-xl) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: var(--shadow-colored) !important;
  position: relative !important;
  overflow: hidden !important;
}

.hero-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.hero-btn-primary:hover {
  background: var(--gradient-primary) !important;
  border-color: var(--primary-600) !important;
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: var(--shadow-glow) !important;
  filter: brightness(1.1) !important;
}

.hero-btn-primary:hover::before {
  left: 100%;
}

.hero-btn-secondary {
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text-primary) !important;
  font-weight: var(--font-weight-medium) !important;
  padding: var(--spacing-lg) calc(var(--spacing-xl) * 1.5) !important;
  border-radius: var(--radius-xl) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: var(--shadow-sm) !important;
}

.hero-btn-secondary:hover {
  background: var(--color-surface-hover) !important;
  border-color: var(--color-border-hover) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-md) !important;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  max-width: 800px;
  margin: 0 auto;
  animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) 1s both;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--color-background-elevated);
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: var(--shadow-sm);
}

.stat-item:hover {
  background: var(--color-surface-hover);
  border-color: var(--primary-200);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid var(--primary-200);
  box-shadow: var(--shadow-sm);
  color: var(--neutral-white);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: 1;
  color: var(--color-heading);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Enhanced Categories Section */
.categories-section {
  margin-bottom: calc(var(--spacing-3xl) * 1.5);
}

.section-header {
  text-align: center;
  margin-bottom: calc(var(--spacing-xl) * 2);
  animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.2s both;
}

.section-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-heading);
  margin-bottom: var(--spacing-md);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-colored);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 500px;
  margin: 0 auto;
  line-height: var(--line-height-relaxed);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: calc(var(--spacing-xl) * 1.5);
  margin-bottom: calc(var(--spacing-3xl) * 1.5);
}

.category-card-wrapper {
  animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) both;
}

.category-card-wrapper.stagger-1 { animation-delay: 0.1s; }
.category-card-wrapper.stagger-2 { animation-delay: 0.2s; }
.category-card-wrapper.stagger-3 { animation-delay: 0.3s; }
.category-card-wrapper.stagger-4 { animation-delay: 0.4s; }
.category-card-wrapper.stagger-5 { animation-delay: 0.5s; }
.category-card-wrapper.stagger-6 { animation-delay: 0.6s; }

.category-card {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-3xl);
  padding: calc(var(--spacing-xl) * 1.5);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  border-radius: var(--radius-3xl) var(--radius-3xl) 0 0;
}

.category-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glass);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  pointer-events: none;
  border-radius: var(--radius-3xl);
}

.category-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
}

.category-card:hover::before {
  transform: scaleX(1);
}

.category-card:hover::after {
  opacity: 1;
}

.category-card:hover .category-icon {
  transform: scale(1.1) rotate(8deg);
  color: var(--primary-500);
}

.category-card:hover .icon-glow {
  opacity: 0.3;
  transform: scale(1.5);
}

.category-icon-wrapper {
  position: relative;
  width: 80px;
  height: 80px;
  background: var(--gradient-primary-soft);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-xl);
  border: 1px solid var(--primary-200);
  box-shadow: var(--shadow-md);
}

.category-icon {
  color: var(--primary-600);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 2;
  position: relative;
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  filter: blur(20px);
  z-index: 1;
}

.category-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.category-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-heading);
  margin-bottom: var(--spacing-sm);
  line-height: var(--line-height-tight);
}

.category-description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
  flex: 1;
}

.category-tools-preview {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-lg);
}

.tool-tag {
  background: var(--primary-50) !important;
  border-color: var(--primary-200) !important;
  color: var(--primary-600) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--font-size-2xs) !important;
}

.more-tools {
  font-size: var(--font-size-2xs);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
  background: var(--neutral-100);
  padding: 2px 8px;
  border-radius: var(--radius-full);
  border: 1px solid var(--neutral-200);
}

.category-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  margin-top: auto;
}

.tool-count {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
}

.arrow-icon {
  color: var(--color-text-tertiary);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.category-card:hover .arrow-icon {
  color: var(--primary-500);
  transform: translateX(4px);
}

/* Features Section */
.features-section {
  margin-top: var(--spacing-3xl);
  padding: var(--spacing-3xl) 0;
  background: var(--color-background-soft);
  border-radius: var(--radius-2xl);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--color-background-elevated);
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-200);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  color: var(--neutral-white);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.feature-item h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-heading);
  margin-bottom: var(--spacing-md);
}

.feature-item p {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Animations */
.home-container {
  animation: fadeIn 0.6s ease-out;
}

.categories-grid .el-col {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.categories-grid .el-col:nth-child(1) { animation-delay: 0.1s; }
.categories-grid .el-col:nth-child(2) { animation-delay: 0.2s; }
.categories-grid .el-col:nth-child(3) { animation-delay: 0.3s; }

.tools-section .el-col {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.tools-section .el-col:nth-child(1) { animation-delay: 0.1s; }
.tools-section .el-col:nth-child(2) { animation-delay: 0.2s; }
.tools-section .el-col:nth-child(3) { animation-delay: 0.3s; }
.tools-section .el-col:nth-child(4) { animation-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 768px) {
  .home-container {
    padding: 0 var(--spacing-md);
  }

  .features-section {
    padding: var(--spacing-xl) var(--spacing-md);
  }

  .feature-item {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
  }

  .feature-icon {
    width: 56px;
    height: 56px;
  }
}

@media (max-width: 480px) {
  .feature-item h3 {
    font-size: var(--font-size-lg);
  }

  .feature-icon {
    width: 48px;
    height: 48px;
  }
}


/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: calc(var(--spacing-3xl) * 1.5) var(--spacing-lg);
    border-radius: var(--radius-3xl);
  }

  .title-main {
    font-size: var(--font-size-4xl);
  }

  .title-sub {
    font-size: var(--font-size-lg);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }

  .hero-btn-primary,
  .hero-btn-secondary {
    width: 100%;
    max-width: 280px;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .stat-item {
    padding: var(--spacing-md);
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .category-card {
    padding: var(--spacing-xl);
  }

  .category-icon-wrapper {
    width: 64px;
    height: 64px;
  }

  .section-title {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .home-container {
    padding: 0 var(--spacing-sm);
  }

  .hero-section {
    padding: var(--spacing-3xl) var(--spacing-md);
    border-radius: var(--radius-2xl);
  }

  .title-main {
    font-size: var(--font-size-3xl);
  }

  .title-sub {
    font-size: var(--font-size-base);
  }

  .hero-subtitle {
    font-size: var(--font-size-sm);
  }

  .hero-badge {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-md);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .stat-number {
    font-size: var(--font-size-xl);
  }

  .category-card {
    padding: var(--spacing-lg);
    border-radius: var(--radius-2xl);
  }

  .category-icon-wrapper {
    width: 56px;
    height: 56px;
  }

  .category-title {
    font-size: var(--font-size-lg);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .section-subtitle {
    font-size: var(--font-size-base);
  }

  .tool-tag {
    font-size: 10px !important;
    padding: 1px 6px !important;
  }

  .more-tools {
    font-size: 10px;
    padding: 1px 6px;
  }
}

@media (max-width: 360px) {
  .hero-actions {
    gap: var(--spacing-sm);
  }

  .hero-btn-primary,
  .hero-btn-secondary {
    padding: var(--spacing-md) var(--spacing-lg) !important;
    font-size: var(--font-size-sm) !important;
  }

  .category-tools-preview {
    gap: 4px;
  }
}
</style>
