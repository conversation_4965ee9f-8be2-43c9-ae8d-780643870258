/* Performance & Loading Experience Optimizations */

/* Skeleton Loading Screens */
.skeleton {
  background: linear-gradient(90deg, 
    var(--color-background-mute) 25%, 
    var(--color-surface-hover) 50%, 
    var(--color-background-mute) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Skeleton Components */
.skeleton-text {
  height: 1em;
  margin-bottom: var(--spacing-2);
}

.skeleton-text.skeleton-text-lg {
  height: 1.5em;
}

.skeleton-text.skeleton-text-sm {
  height: 0.75em;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
}

.skeleton-avatar.skeleton-avatar-lg {
  width: 64px;
  height: 64px;
}

.skeleton-avatar.skeleton-avatar-sm {
  width: 32px;
  height: 32px;
}

.skeleton-card {
  padding: var(--spacing-4);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  background: var(--color-background-elevated);
}

.skeleton-card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.skeleton-card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.skeleton-button {
  height: 40px;
  width: 120px;
  border-radius: var(--radius-lg);
}

.skeleton-image {
  width: 100%;
  height: 200px;
  border-radius: var(--radius-lg);
}

/* Lazy Loading Placeholders */
.lazy-image-container {
  position: relative;
  overflow: hidden;
  background: var(--color-background-soft);
  border-radius: var(--radius-lg);
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.lazy-image.loaded {
  opacity: 1;
}

.lazy-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background-soft);
  color: var(--color-text-tertiary);
  font-size: var(--font-size-sm);
}

/* Progressive Enhancement */
.progressive-enhancement {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.progressive-enhancement.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Critical Resource Loading */
.critical-resource-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--color-background-soft);
  z-index: 9999;
}

.critical-resource-progress {
  height: 100%;
  background: var(--gradient-primary);
  width: 0%;
  transition: width 0.3s ease;
  border-radius: 0 3px 3px 0;
}

/* Optimized Animations */
.optimized-animation {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.optimized-animation.animation-complete {
  will-change: auto;
}

/* Intersection Observer Optimizations */
.intersection-observer-target {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.intersection-observer-target.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Content Loading States */
.content-loading {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background-soft);
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border);
}

.content-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.content-loading-text {
  margin-top: var(--spacing-3);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Virtualized List Optimizations */
.virtual-list-container {
  height: 400px;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.virtual-list-item {
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.2s ease;
}

.virtual-list-item:hover {
  background: var(--color-surface-hover);
}

.virtual-list-placeholder {
  height: 60px;
  background: var(--color-background-soft);
  border-radius: var(--radius-md);
  margin: var(--spacing-2) 0;
}

/* Image Optimization */
.optimized-image {
  max-width: 100%;
  height: auto;
  loading: lazy;
  decoding: async;
}

.optimized-image[data-src] {
  filter: blur(5px);
  transition: filter 0.3s ease;
}

.optimized-image.loaded {
  filter: blur(0);
}

/* Font Loading Optimization */
.font-loading {
  font-display: swap;
  visibility: hidden;
}

.font-loaded {
  visibility: visible;
}

/* Critical CSS Inlining */
.critical-above-fold {
  contain: layout style paint;
}

.critical-below-fold {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* Resource Hints */
.preload-hint {
  position: relative;
}

.preload-hint::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  pointer-events: none;
}

/* Performance Monitoring */
.performance-marker {
  position: absolute;
  top: -1px;
  left: -1px;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .skeleton {
    animation: none;
    background: var(--color-background-mute);
  }
  
  .progressive-enhancement,
  .intersection-observer-target {
    transition: none;
    opacity: 1;
    transform: none;
  }
  
  .optimized-animation {
    animation: none;
    transition: none;
  }
}

/* High Contrast Support */
@media (prefers-contrast: high) {
  .skeleton {
    background: var(--color-border);
  }
  
  .content-loading {
    border-width: 2px;
  }
  
  .lazy-image-placeholder {
    border: 2px solid var(--color-border);
  }
}

/* Print Optimizations */
@media print {
  .skeleton,
  .content-loading,
  .lazy-image-placeholder {
    display: none;
  }
  
  .progressive-enhancement,
  .intersection-observer-target {
    opacity: 1;
    transform: none;
  }
}

/* Memory Optimization */
.memory-optimized {
  contain: strict;
  content-visibility: auto;
}

.memory-optimized.out-of-view {
  contain-intrinsic-size: 0 200px;
}

/* Network-aware Loading */
@media (prefers-reduced-data: reduce) {
  .skeleton {
    animation-duration: 3s;
  }
  
  .progressive-enhancement {
    transition-duration: 0.3s;
  }
  
  .optimized-image {
    loading: lazy;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .optimized-animation {
    animation-duration: 0.3s;
  }
  
  .progressive-enhancement {
    transition-duration: 0.4s;
  }
}

/* Dark Mode Performance */
@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, 
      var(--neutral-800) 25%, 
      var(--neutral-700) 50%, 
      var(--neutral-800) 75%);
  }
  
  .content-loading {
    background: var(--neutral-900);
    border-color: var(--neutral-700);
  }
  
  .lazy-image-placeholder {
    background: var(--neutral-800);
    color: var(--neutral-400);
  }
}
