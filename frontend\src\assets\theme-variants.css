/* Theme Variants for Different Tool Categories */

/* Text Tools Theme */
.theme-text {
  --current-primary: var(--text-primary);
  --current-secondary: var(--text-secondary);
  --current-gradient: var(--gradient-text);
  --current-shadow: var(--shadow-text);
}

.theme-text .tool-card:hover {
  border-color: var(--text-primary);
  box-shadow: var(--shadow-text);
}

.theme-text .tool-icon {
  background: var(--gradient-text);
}

.theme-text .tool-btn {
  background: var(--gradient-text) !important;
  box-shadow: var(--shadow-text) !important;
}

/* Development Tools Theme */
.theme-dev {
  --current-primary: var(--dev-primary);
  --current-secondary: var(--dev-secondary);
  --current-gradient: var(--gradient-dev);
  --current-shadow: var(--shadow-dev);
}

.theme-dev .tool-card:hover {
  border-color: var(--dev-primary);
  box-shadow: var(--shadow-dev);
}

.theme-dev .tool-icon {
  background: var(--gradient-dev);
}

.theme-dev .tool-btn {
  background: var(--gradient-dev) !important;
  box-shadow: var(--shadow-dev) !important;
}

/* File Tools Theme */
.theme-file {
  --current-primary: var(--file-primary);
  --current-secondary: var(--file-secondary);
  --current-gradient: var(--gradient-file);
  --current-shadow: var(--shadow-file);
}

.theme-file .tool-card:hover {
  border-color: var(--file-primary);
  box-shadow: var(--shadow-file);
}

.theme-file .tool-icon {
  background: var(--gradient-file);
}

.theme-file .tool-btn {
  background: var(--gradient-file) !important;
  box-shadow: var(--shadow-file) !important;
}

/* Calculator Tools Theme */
.theme-calc {
  --current-primary: var(--calc-primary);
  --current-secondary: var(--calc-secondary);
  --current-gradient: var(--gradient-calc);
  --current-shadow: var(--shadow-calc);
}

.theme-calc .tool-card:hover {
  border-color: var(--calc-primary);
  box-shadow: var(--shadow-calc);
}

.theme-calc .tool-icon {
  background: var(--gradient-calc);
}

.theme-calc .tool-btn {
  background: var(--gradient-calc) !important;
  box-shadow: var(--shadow-calc) !important;
}

/* Media Tools Theme */
.theme-media {
  --current-primary: var(--media-primary);
  --current-secondary: var(--media-secondary);
  --current-gradient: var(--gradient-media);
  --current-shadow: var(--shadow-media);
}

.theme-media .tool-card:hover {
  border-color: var(--media-primary);
  box-shadow: var(--shadow-media);
}

.theme-media .tool-icon {
  background: var(--gradient-media);
}

.theme-media .tool-btn {
  background: var(--gradient-media) !important;
  box-shadow: var(--shadow-media) !important;
}

/* Education Tools Theme */
.theme-edu {
  --current-primary: var(--edu-primary);
  --current-secondary: var(--edu-secondary);
  --current-gradient: var(--gradient-edu);
  --current-shadow: var(--shadow-edu);
}

.theme-edu .tool-card:hover {
  border-color: var(--edu-primary);
  box-shadow: var(--shadow-edu);
}

.theme-edu .tool-icon {
  background: var(--gradient-edu);
}

.theme-edu .tool-btn {
  background: var(--gradient-edu) !important;
  box-shadow: var(--shadow-edu) !important;
}

/* Enhanced Category Cards with Theme Support */
.category-card-text {
  background: linear-gradient(135deg, 
    rgba(30, 64, 175, 0.02) 0%, 
    rgba(5, 150, 105, 0.02) 100%);
  border-color: rgba(30, 64, 175, 0.1);
}

.category-card-text:hover {
  background: linear-gradient(135deg, 
    rgba(30, 64, 175, 0.05) 0%, 
    rgba(5, 150, 105, 0.05) 100%);
  border-color: rgba(30, 64, 175, 0.2);
  box-shadow: var(--shadow-text);
}

.category-card-dev {
  background: linear-gradient(135deg, 
    rgba(124, 58, 237, 0.02) 0%, 
    rgba(168, 85, 247, 0.02) 100%);
  border-color: rgba(124, 58, 237, 0.1);
}

.category-card-dev:hover {
  background: linear-gradient(135deg, 
    rgba(124, 58, 237, 0.05) 0%, 
    rgba(168, 85, 247, 0.05) 100%);
  border-color: rgba(124, 58, 237, 0.2);
  box-shadow: var(--shadow-dev);
}

.category-card-file {
  background: linear-gradient(135deg, 
    rgba(220, 38, 38, 0.02) 0%, 
    rgba(249, 115, 22, 0.02) 100%);
  border-color: rgba(220, 38, 38, 0.1);
}

.category-card-file:hover {
  background: linear-gradient(135deg, 
    rgba(220, 38, 38, 0.05) 0%, 
    rgba(249, 115, 22, 0.05) 100%);
  border-color: rgba(220, 38, 38, 0.2);
  box-shadow: var(--shadow-file);
}

.category-card-calc {
  background: linear-gradient(135deg, 
    rgba(8, 145, 178, 0.02) 0%, 
    rgba(6, 182, 212, 0.02) 100%);
  border-color: rgba(8, 145, 178, 0.1);
}

.category-card-calc:hover {
  background: linear-gradient(135deg, 
    rgba(8, 145, 178, 0.05) 0%, 
    rgba(6, 182, 212, 0.05) 100%);
  border-color: rgba(8, 145, 178, 0.2);
  box-shadow: var(--shadow-calc);
}

.category-card-media {
  background: linear-gradient(135deg, 
    rgba(190, 24, 93, 0.02) 0%, 
    rgba(236, 72, 153, 0.02) 100%);
  border-color: rgba(190, 24, 93, 0.1);
}

.category-card-media:hover {
  background: linear-gradient(135deg, 
    rgba(190, 24, 93, 0.05) 0%, 
    rgba(236, 72, 153, 0.05) 100%);
  border-color: rgba(190, 24, 93, 0.2);
  box-shadow: var(--shadow-media);
}

.category-card-edu {
  background: linear-gradient(135deg, 
    rgba(202, 138, 4, 0.02) 0%, 
    rgba(234, 179, 8, 0.02) 100%);
  border-color: rgba(202, 138, 4, 0.1);
}

.category-card-edu:hover {
  background: linear-gradient(135deg, 
    rgba(202, 138, 4, 0.05) 0%, 
    rgba(234, 179, 8, 0.05) 100%);
  border-color: rgba(202, 138, 4, 0.2);
  box-shadow: var(--shadow-edu);
}

/* Dark Mode Adjustments for Themes */
@media (prefers-color-scheme: dark) {
  .category-card-text,
  .category-card-dev,
  .category-card-file,
  .category-card-calc,
  .category-card-media,
  .category-card-edu {
    background: var(--color-background-elevated);
  }
  
  .category-card-text:hover {
    background: linear-gradient(135deg, 
      rgba(30, 64, 175, 0.1) 0%, 
      rgba(5, 150, 105, 0.1) 100%);
  }
  
  .category-card-dev:hover {
    background: linear-gradient(135deg, 
      rgba(124, 58, 237, 0.1) 0%, 
      rgba(168, 85, 247, 0.1) 100%);
  }
  
  .category-card-file:hover {
    background: linear-gradient(135deg, 
      rgba(220, 38, 38, 0.1) 0%, 
      rgba(249, 115, 22, 0.1) 100%);
  }
  
  .category-card-calc:hover {
    background: linear-gradient(135deg, 
      rgba(8, 145, 178, 0.1) 0%, 
      rgba(6, 182, 212, 0.1) 100%);
  }
  
  .category-card-media:hover {
    background: linear-gradient(135deg, 
      rgba(190, 24, 93, 0.1) 0%, 
      rgba(236, 72, 153, 0.1) 100%);
  }
  
  .category-card-edu:hover {
    background: linear-gradient(135deg, 
      rgba(202, 138, 4, 0.1) 0%, 
      rgba(234, 179, 8, 0.1) 100%);
  }
}
