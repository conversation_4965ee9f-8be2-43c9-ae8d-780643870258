/* 现代化动画和交互效果系统 */

/* 高级动画 easing 函数 */
:root {
  /* 自定义贝塞尔曲线 */
  --ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
  --ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-out-quint: cubic-bezier(0.23, 1, 0.320, 1);
  --ease-in-out-quart: cubic-bezier(0.77, 0, 0.175, 1);
  --ease-in-out-expo: cubic-bezier(0.87, 0, 0.13, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* 动画持续时间 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 800ms;
}

/* 页面加载动画 */
.page-enter {
  animation: pageEnter 0.8s var(--ease-out-expo) forwards;
}

@keyframes pageEnter {
  0% {
    opacity: 0;
    transform: translateY(32px) scale(0.98);
    filter: blur(4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* 元素入场动画 */
.animate-fade-in {
  animation: fadeIn 0.6s var(--ease-out-quart) forwards;
}

.animate-slide-up {
  animation: slideUp 0.6s var(--ease-out-expo) forwards;
}

.animate-slide-down {
  animation: slideDown 0.6s var(--ease-out-expo) forwards;
}

.animate-slide-left {
  animation: slideLeft 0.6s var(--ease-out-expo) forwards;
}

.animate-slide-right {
  animation: slideRight 0.6s var(--ease-out-expo) forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s var(--ease-out-back) forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.8s var(--ease-bounce) forwards;
}

.animate-rotate-in {
  animation: rotateIn 0.6s var(--ease-out-expo) forwards;
}

.animate-blur-in {
  animation: blurIn 0.8s var(--ease-out-quart) forwards;
}

/* 交错动画延迟 */
.stagger-delay-1 { animation-delay: 0.1s; }
.stagger-delay-2 { animation-delay: 0.2s; }
.stagger-delay-3 { animation-delay: 0.3s; }
.stagger-delay-4 { animation-delay: 0.4s; }
.stagger-delay-5 { animation-delay: 0.5s; }
.stagger-delay-6 { animation-delay: 0.6s; }

/* 悬停效果动画 */
.hover-lift {
  transition: transform var(--duration-normal) var(--ease-out-quart),
              box-shadow var(--duration-normal) var(--ease-out-quart);
}

.hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
}

.hover-glow {
  transition: box-shadow var(--duration-normal) var(--ease-out-quart);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

.hover-scale {
  transition: transform var(--duration-normal) var(--ease-out-back);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-tilt {
  transition: transform var(--duration-normal) var(--ease-out-quart);
}

.hover-tilt:hover {
  transform: rotate(2deg) scale(1.02);
}

.hover-slide {
  position: relative;
  overflow: hidden;
}

.hover-slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--duration-slow) var(--ease-out-quart);
}

.hover-slide:hover::before {
  left: 100%;
}

/* 加载动画 */
.loading-skeleton {
  background: var(--color-background-mute);
  background-image: linear-gradient(
    90deg,
    var(--color-background-mute),
    var(--color-surface-hover),
    var(--color-background-mute)
  );
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading-spinner {
  animation: spinner 1s linear infinite;
}

@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* 状态过渡动画 */
.state-enter {
  animation: stateEnter var(--duration-normal) var(--ease-out-expo);
}

.state-exit {
  animation: stateExit var(--duration-fast) var(--ease-in-out-quart);
}

@keyframes stateEnter {
  0% {
    opacity: 0;
    transform: translateY(8px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes stateExit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.98);
  }
}

/* 通知和消息动画 */
.notification-enter {
  animation: notificationEnter var(--duration-slow) var(--ease-out-back);
}

.notification-exit {
  animation: notificationExit var(--duration-normal) var(--ease-in-out-quart);
}

@keyframes notificationEnter {
  0% {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes notificationExit {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
}

/* 模态框动画 */
.modal-enter {
  animation: modalEnter var(--duration-slower) var(--ease-out-expo);
}

.modal-exit {
  animation: modalExit var(--duration-normal) var(--ease-in-out-quart);
}

@keyframes modalEnter {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
    filter: blur(4px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}

@keyframes modalExit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 高级悬停效果 */
.hover-glow-primary {
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.hover-glow-primary:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

.hover-glow-success {
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.hover-glow-success:hover {
  box-shadow: 0 0 30px rgba(16, 185, 129, 0.4);
  transform: translateY(-2px);
}

.hover-glow-warning {
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.hover-glow-warning:hover {
  box-shadow: 0 0 30px rgba(245, 158, 11, 0.4);
  transform: translateY(-2px);
}

.hover-glow-error {
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.hover-glow-error:hover {
  box-shadow: 0 0 30px rgba(239, 68, 68, 0.4);
  transform: translateY(-2px);
}

/* 视差滚动效果 */
.parallax-slow {
  transform: translateY(var(--parallax-offset-slow, 0));
  transition: transform 0.1s ease-out;
}

.parallax-normal {
  transform: translateY(var(--parallax-offset-normal, 0));
  transition: transform 0.1s ease-out;
}

.parallax-fast {
  transform: translateY(var(--parallax-offset-fast, 0));
  transition: transform 0.1s ease-out;
}

/* 无障碍和性能优化 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .hover-slide::before {
    display: none;
  }

  .loading-shimmer {
    animation: none;
    background-image: none;
  }
}

/* GPU 硬件加速优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* 触摸设备优化 */
@media (hover: none) {
  .hover-lift:hover,
  .hover-glow:hover,
  .hover-scale:hover,
  .hover-tilt:hover {
    transform: none;
    box-shadow: none;
  }

  .hover-lift:active {
    transform: translateY(-2px) scale(1.01);
  }

  .hover-scale:active {
    transform: scale(1.02);
  }
}

/* 自定义滚动行为 */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* 文本动画效果 */
.text-gradient-animate {
  background: linear-gradient(45deg, var(--primary-500), var(--accent-purple), var(--accent-cyan));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGradientShift 3s ease-in-out infinite;
}

@keyframes textGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.text-typing {
  overflow: hidden;
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-cursor 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-cursor {
  from, to { border-color: transparent; }
  50% { border-color: var(--color-text-primary); }
}

/* 进度指示器动画 */
.progress-indeterminate {
  position: relative;
  overflow: hidden;
}

.progress-indeterminate::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  animation: progressIndeterminate 1.5s ease-in-out infinite;
}

@keyframes progressIndeterminate {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 高级 CSS 变换效果 */
.flip-card {
  perspective: 1000px;
}

.flip-card-inner {
  transition: transform var(--duration-slow) var(--ease-out-expo);
  transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  backface-visibility: hidden;
}

.flip-card-back {
  transform: rotateY(180deg);
}

/* 悬浮效果增强 */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

/* 波浪动画 */
.wave-animation {
  animation: wave 2s ease-in-out infinite;
  transform-origin: 70% 70%;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  10%, 30%, 50%, 70%, 90% { transform: rotate(14deg); }
  20%, 40%, 60%, 80% { transform: rotate(-8deg); }
}

/* 呼吸效果 */
.breathing {
  animation: breathing 3s ease-in-out infinite;
}

@keyframes breathing {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

/* 震动效果 */
.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* 成功动画 */
.success-checkmark {
  animation: successCheckmark 0.6s var(--ease-out-back) forwards;
}

@keyframes successCheckmark {
  0% {
    transform: scale(0) rotate(45deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(45deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(45deg);
    opacity: 1;
  }
}

/* 聚焦环效果 */
.focus-ring {
  position: relative;
}

.focus-ring::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--primary-500);
  border-radius: inherit;
  opacity: 0;
  transform: scale(0.95);
  transition: all var(--duration-normal) var(--ease-out-quart);
  pointer-events: none;
}

.focus-ring:focus::after {
  opacity: 1;
  transform: scale(1);
}

/* 微交互反馈 */
.tap-feedback:active {
  transform: scale(0.98);
  transition: transform var(--duration-fast) var(--ease-out-quart);
}

.press-feedback:active {
  transform: scale(0.95);
  opacity: 0.8;
  transition: all var(--duration-fast) var(--ease-out-quart);
}

/* 提示工具动画 */
.tooltip-enter {
  animation: tooltipEnter var(--duration-normal) var(--ease-out-back);
}

@keyframes tooltipEnter {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(4px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced Micro-interactions */
.micro-bounce {
  animation: micro-bounce 0.6s var(--ease-bounce) both;
}

.micro-shake {
  animation: micro-shake 0.5s ease-in-out both;
}

.micro-glow {
  animation: micro-glow 2s ease-in-out infinite alternate;
}

@keyframes micro-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes micro-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes micro-glow {
  0% { box-shadow: var(--shadow-sm); }
  100% { box-shadow: var(--shadow-glow); }
}

/* Loading State Animations */
.loading-shimmer {
  background: linear-gradient(90deg,
    var(--color-background-mute) 25%,
    var(--color-surface-hover) 50%,
    var(--color-background-mute) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.loading-pulse {
  animation: loading-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes loading-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Success/Error State Animations */
.success-bounce {
  animation: success-bounce 0.6s var(--ease-bounce) both;
}

.error-shake {
  animation: error-shake 0.5s ease-in-out both;
}

@keyframes success-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes error-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

/* 响应式动画调整 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

@media (max-width: 768px) {
  .animate-bounce,
  .animate-pulse,
  .animate-float {
    animation-duration: 0.5s;
  }

  .hover-lift:hover {
    transform: translateY(-2px) scale(1.01);
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }
}