<template>
  <!-- Enhanced Loading Overlay -->
  <div v-if="loading" class="loading-overlay">
    <div class="loading-content">
      <div class="loading-spinner-enhanced"></div>
      <p class="loading-text">{{ loadingText || '处理中...' }}</p>
    </div>
  </div>

  <!-- Enhanced Notification System -->
  <Teleport to="body">
    <div v-for="notification in notifications" :key="notification.id" 
         :class="['notification-enhanced', `notification-${notification.type}`]">
      <div class="notification-header">
        <div class="notification-icon">
          <el-icon v-if="notification.type === 'success'"><Check /></el-icon>
          <el-icon v-else-if="notification.type === 'error'"><Close /></el-icon>
          <el-icon v-else-if="notification.type === 'warning'"><Warning /></el-icon>
          <el-icon v-else><InfoFilled /></el-icon>
        </div>
        <div class="notification-content">
          <h4 class="notification-title">{{ notification.title }}</h4>
          <p class="notification-message">{{ notification.message }}</p>
        </div>
        <button @click="removeNotification(notification.id)" class="notification-close">
          <el-icon><Close /></el-icon>
        </button>
      </div>
    </div>
  </Teleport>

  <!-- Enhanced Progress Ring -->
  <div v-if="showProgress" class="progress-ring">
    <svg>
      <circle class="progress-ring-bg" cx="30" cy="30" r="25"></circle>
      <circle class="progress-ring-fill" cx="30" cy="30" r="25" 
              :style="{ strokeDashoffset: progressOffset }"></circle>
    </svg>
    <div class="progress-text">{{ Math.round(progress) }}%</div>
  </div>

  <!-- Enhanced Drag and Drop Zone -->
  <div v-if="showDragDrop" 
       class="drag-drop-zone"
       :class="{ 'drag-over': isDragOver }"
       @dragover.prevent="handleDragOver"
       @dragleave.prevent="handleDragLeave"
       @drop.prevent="handleDrop">
    <div class="drag-drop-content">
      <div class="drag-drop-icon">
        <el-icon><Upload /></el-icon>
      </div>
      <h3>拖拽文件到此处</h3>
      <p>或点击选择文件</p>
      <input type="file" ref="fileInput" @change="handleFileSelect" style="display: none;" multiple>
      <button @click="$refs.fileInput?.click()" class="btn-enhanced btn-primary-enhanced">
        选择文件
      </button>
    </div>
  </div>

  <!-- Enhanced Modal -->
  <Teleport to="body">
    <div v-if="showModal" class="modal-overlay" @click.self="closeModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ modalTitle }}</h3>
          <button @click="closeModal" class="modal-close">
            <el-icon><Close /></el-icon>
          </button>
        </div>
        <div class="modal-body">
          <slot name="modal-content"></slot>
        </div>
        <div class="modal-footer">
          <button @click="closeModal" class="btn-enhanced btn-secondary-enhanced">取消</button>
          <button @click="confirmModal" class="btn-enhanced btn-primary-enhanced">确认</button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Check, Close, Warning, InfoFilled, Upload } from '@element-plus/icons-vue'

// Props
interface Props {
  loading?: boolean
  loadingText?: string
  showProgress?: boolean
  progress?: number
  showDragDrop?: boolean
  showModal?: boolean
  modalTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  loadingText: '处理中...',
  showProgress: false,
  progress: 0,
  showDragDrop: false,
  showModal: false,
  modalTitle: '确认操作'
})

// Emits
const emit = defineEmits<{
  filesSelected: [files: FileList]
  modalConfirm: []
  modalClose: []
}>()

// Reactive data
const notifications = ref<Array<{
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
}>>([])

const isDragOver = ref(false)
const fileInput = ref<HTMLInputElement>()

// Computed
const progressOffset = computed(() => {
  const circumference = 2 * Math.PI * 25
  return circumference - (props.progress / 100) * circumference
})

// Methods
const showNotification = (type: 'success' | 'error' | 'warning' | 'info', title: string, message: string) => {
  const id = Date.now().toString()
  notifications.value.push({ id, type, title, message })
  
  // Auto remove after 5 seconds
  setTimeout(() => {
    removeNotification(id)
  }, 5000)
}

const removeNotification = (id: string) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
  
  if (e.dataTransfer?.files) {
    emit('filesSelected', e.dataTransfer.files)
  }
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  if (target.files) {
    emit('filesSelected', target.files)
  }
}

const closeModal = () => {
  emit('modalClose')
}

const confirmModal = () => {
  emit('modalConfirm')
}

// Expose methods for parent components
defineExpose({
  showNotification,
  removeNotification
})

// Lifecycle
onMounted(() => {
  // Add global keyboard shortcuts
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

const handleKeydown = (e: KeyboardEvent) => {
  // ESC to close modal
  if (e.key === 'Escape' && props.showModal) {
    closeModal()
  }
}
</script>

<style scoped>
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}

.notification-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  margin-top: 2px;
}

.notification-success .notification-icon {
  background: var(--success-100);
  color: var(--success-600);
}

.notification-error .notification-icon {
  background: var(--error-100);
  color: var(--error-600);
}

.notification-warning .notification-icon {
  background: var(--warning-100);
  color: var(--warning-600);
}

.notification-info .notification-icon {
  background: var(--primary-100);
  color: var(--primary-600);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.notification-message {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.notification-close {
  flex-shrink: 0;
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.notification-close:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-secondary);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--color-border);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.modal-close {
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-secondary);
}

.modal-body {
  margin-bottom: var(--spacing-4);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--color-border);
}
</style>
