@import './base.css';
@import './components.css';
@import './element-theme.css';
@import './animations.css';
@import './tool-layout.css';
@import './enhanced-ui.css';
@import './theme-variants.css';
@import './icon-system.css';
@import './interaction-enhancements.css';
@import './responsive-enhancements.css';
@import './performance-optimizations.css';

/* App Layout */
#app {
  min-height: 100vh;
  background: var(--color-background);
}

/* Layout Container */
.layout-container {
  min-height: 100vh;
  background: var(--color-background);
}

/* Sidebar Styles */
.sidebar {
  background: var(--color-background-elevated);
  border-right: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 100;
}

.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  background: var(--gradient-primary);
  position: relative;
  overflow: hidden;
}

.logo-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}

.logo-text {
  color: var(--neutral-white);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  position: relative;
  z-index: 1;
}

.logo-icon {
  color: var(--neutral-white);
  position: relative;
  z-index: 1;
}

/* Sidebar Menu */
.sidebar-menu {
  border: none !important;
  background: transparent !important;
}

.sidebar-menu .el-menu-item,
.sidebar-menu .el-sub-menu__title {
  height: 48px;
  line-height: 48px;
  margin: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
  color: var(--color-text-secondary);
  border-bottom: none !important;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-sub-menu__title:hover {
  background: var(--color-surface-hover) !important;
  color: var(--color-text-primary) !important;
  transform: translateX(2px);
}

.sidebar-menu .el-menu-item.is-active {
  background: var(--gradient-primary) !important;
  color: var(--neutral-white) !important;
  box-shadow: var(--shadow-md);
}

.sidebar-menu .el-sub-menu .el-menu-item {
  margin-left: var(--spacing-lg);
  font-size: var(--font-size-sm);
}

.sidebar-menu .el-sub-menu .el-menu-item:hover {
  background: var(--primary-50) !important;
  color: var(--primary-700) !important;
}

/* Main Content */
.main-content {
  background: var(--color-background-soft);
  min-height: 100vh;
  overflow-y: auto;
}

/* Header */
.header {
  height: 64px;
  background: var(--color-background-elevated);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.collapse-btn {
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text-secondary) !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.2s ease !important;
}

.collapse-btn:hover {
  background: var(--color-surface-hover) !important;
  border-color: var(--color-border-hover) !important;
  color: var(--color-text-primary) !important;
  transform: scale(1.05);
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-heading);
  margin: 0;
}

/* Content Area - Optimized for better space utilization */
.content-area {
  padding: var(--spacing-lg) var(--spacing-xl);
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
}

/* Tool Container Optimization */
.tool-container {
  max-width: none;
  width: 100%;
}

.tool-card {
  width: 100%;
  max-width: none;
}

/* Input/Output Layout Optimization */
.io-section {
  width: 100%;
}

.io-section .el-row {
  width: 100%;
  margin: 0 !important;
}

.io-section .el-col {
  padding: 0 8px !important;
}

/* Full width text areas */
.text-area,
.el-textarea__inner {
  width: 100% !important;
  min-height: 400px !important;
}

/* Responsive grid for different screen sizes */
@media (min-width: 1200px) {
  .content-area {
    padding: var(--spacing-lg) var(--spacing-2xl);
  }

  .io-section .el-col {
    padding: 0 12px !important;
  }

  .text-area,
  .el-textarea__inner {
    min-height: 500px !important;
  }
}

@media (min-width: 1600px) {
  .content-area {
    max-width: 1800px;
    padding: var(--spacing-xl) var(--spacing-3xl);
  }

  .text-area,
  .el-textarea__inner {
    min-height: 600px !important;
  }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .content-area {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .sidebar.mobile-open {
    left: 0;
  }

  .main-content {
    margin-left: 0;
  }

  .content-area {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .header {
    padding: 0 var(--spacing-md);
  }

  /* Mobile: Stack vertically */
  .io-section .el-col {
    width: 100% !important;
    flex: 0 0 100% !important;
    max-width: 100% !important;
    margin-bottom: var(--spacing-lg);
  }

  .text-area,
  .el-textarea__inner {
    min-height: 300px !important;
  }
}

@media (max-width: 480px) {
  .content-area {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .page-title {
    font-size: var(--font-size-lg);
  }

  .io-section .el-col {
    padding: 0 4px !important;
  }

  .text-area,
  .el-textarea__inner {
    min-height: 250px !important;
  }
}
