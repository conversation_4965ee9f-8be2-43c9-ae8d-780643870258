/* Responsive Design Enhancements */

/* Mobile-First Approach - Base Styles */
.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Enhanced Grid System */
.grid-responsive {
  display: grid;
  gap: var(--spacing-4);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
  }
}

@media (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-8);
  }
}

/* Enhanced Mobile Navigation */
.mobile-nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-primary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.mobile-nav-toggle:hover {
  background: var(--color-surface-hover);
}

@media (max-width: 768px) {
  .mobile-nav-toggle {
    display: block;
  }
}

/* Enhanced Mobile Cards */
.card-mobile-enhanced {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

@media (max-width: 768px) {
  .card-mobile-enhanced {
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-3);
  }
}

@media (max-width: 480px) {
  .card-mobile-enhanced {
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
  }
}

/* Enhanced Touch Targets */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.touch-target:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* Enhanced Mobile Forms */
.form-mobile-enhanced {
  width: 100%;
}

.form-mobile-enhanced .form-group {
  margin-bottom: var(--spacing-4);
}

.form-mobile-enhanced .form-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: 16px; /* Prevents zoom on iOS */
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  background: var(--color-surface);
  transition: all 0.3s ease;
}

.form-mobile-enhanced .form-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

@media (max-width: 768px) {
  .form-mobile-enhanced .form-input {
    padding: var(--spacing-4);
    font-size: 16px;
    border-radius: var(--radius-xl);
  }
}

/* Enhanced Mobile Tables */
.table-mobile-enhanced {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-background-elevated);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-mobile-enhanced th,
.table-mobile-enhanced td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
}

.table-mobile-enhanced th {
  background: var(--color-background-soft);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
}

@media (max-width: 768px) {
  .table-mobile-enhanced {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .table-mobile-enhanced th,
  .table-mobile-enhanced td {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 480px) {
  .table-mobile-enhanced {
    font-size: var(--font-size-xs);
  }
  
  .table-mobile-enhanced th,
  .table-mobile-enhanced td {
    padding: var(--spacing-1) var(--spacing-2);
  }
}

/* Enhanced Mobile Modals */
.modal-mobile-enhanced {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: var(--spacing-4);
}

.modal-mobile-enhanced .modal-content {
  background: var(--color-background-elevated);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--color-border);
}

@media (max-width: 768px) {
  .modal-mobile-enhanced {
    padding: var(--spacing-2);
    align-items: flex-end;
  }
  
  .modal-mobile-enhanced .modal-content {
    width: 100%;
    max-width: none;
    max-height: 85vh;
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: var(--spacing-4);
  }
}

/* Enhanced Mobile Buttons */
.btn-mobile-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  min-height: 44px;
  text-decoration: none;
}

.btn-mobile-enhanced:active {
  transform: scale(0.95);
}

@media (max-width: 768px) {
  .btn-mobile-enhanced {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-base);
    border-radius: var(--radius-xl);
    min-height: 48px;
  }
}

/* Enhanced Mobile Typography */
@media (max-width: 768px) {
  .text-mobile-lg {
    font-size: var(--font-size-xl);
    line-height: 1.4;
  }
  
  .text-mobile-base {
    font-size: var(--font-size-lg);
    line-height: 1.5;
  }
  
  .text-mobile-sm {
    font-size: var(--font-size-base);
    line-height: 1.5;
  }
}

/* Enhanced Mobile Spacing */
@media (max-width: 768px) {
  .spacing-mobile-xs { margin: var(--spacing-1); }
  .spacing-mobile-sm { margin: var(--spacing-2); }
  .spacing-mobile-md { margin: var(--spacing-3); }
  .spacing-mobile-lg { margin: var(--spacing-4); }
  .spacing-mobile-xl { margin: var(--spacing-6); }
  
  .padding-mobile-xs { padding: var(--spacing-1); }
  .padding-mobile-sm { padding: var(--spacing-2); }
  .padding-mobile-md { padding: var(--spacing-3); }
  .padding-mobile-lg { padding: var(--spacing-4); }
  .padding-mobile-xl { padding: var(--spacing-6); }
}

/* Enhanced Mobile Utilities */
.hide-mobile {
  display: block;
}

.show-mobile {
  display: none;
}

@media (max-width: 768px) {
  .hide-mobile {
    display: none;
  }
  
  .show-mobile {
    display: block;
  }
}

/* Enhanced Mobile Scrolling */
.scroll-mobile-enhanced {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) transparent;
}

.scroll-mobile-enhanced::-webkit-scrollbar {
  height: 4px;
}

.scroll-mobile-enhanced::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-mobile-enhanced::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-full);
}

.scroll-mobile-enhanced::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

/* Enhanced Mobile Safe Areas */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(var(--spacing-4), env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(var(--spacing-4), env(safe-area-inset-bottom));
  }
  
  .safe-area-inset-left {
    padding-left: max(var(--spacing-4), env(safe-area-inset-left));
  }
  
  .safe-area-inset-right {
    padding-right: max(var(--spacing-4), env(safe-area-inset-right));
  }
}

/* Enhanced Mobile Performance */
@media (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: transparent;
  }
  
  .performance-optimized {
    will-change: transform;
    transform: translateZ(0);
  }
  
  .performance-optimized:hover {
    will-change: auto;
  }
}
