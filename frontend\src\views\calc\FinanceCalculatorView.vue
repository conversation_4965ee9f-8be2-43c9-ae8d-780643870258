<template>
  <div class="finance-calculator-container theme-calc">
    <el-card class="tool-card card-enhanced">
      <template #header>
        <div class="card-header">
          <div class="icon-container icon-container-calc icon-md">
            <el-icon><Money /></el-icon>
          </div>
          <span class="header-title">财务计算器</span>
        </div>
      </template>

      <!-- 计算器类型选择 -->
      <div class="calculator-tabs">
        <el-tabs v-model="activeCalculator" @tab-change="handleCalculatorChange">
          <el-tab-pane label="贷款计算" name="loan">
            <div class="calculator-content">
              <el-row :gutter="30">
                <el-col :span="12">
                  <div class="input-section">
                    <h3 class="section-title">贷款信息</h3>

                    <!-- 贷款种类选择 - 移至顶部 -->
                    <el-form-item label="贷款种类">
                      <el-select
                        v-model="loanData.loanType"
                        placeholder="请选择贷款种类"
                        style="width: 100%"
                        @change="handleLoanTypeChange"
                      >
                        <el-option value="house" label="房贷">
                          <span>房贷</span>
                          <span style="float: right; color: #8492a6; font-size: 13px;">住房按揭贷款</span>
                        </el-option>
                        <el-option value="car" label="车贷">
                          <span>车贷</span>
                          <span style="float: right; color: #8492a6; font-size: 13px;">汽车消费贷款</span>
                        </el-option>
                        <el-option value="business" label="经营贷">
                          <span>经营贷</span>
                          <span style="float: right; color: #8492a6; font-size: 13px;">企业经营贷款</span>
                        </el-option>
                        <el-option value="personal" label="个人贷">
                          <span>个人贷</span>
                          <span style="float: right; color: #8492a6; font-size: 13px;">个人消费贷款</span>
                        </el-option>
                      </el-select>
                    </el-form-item>

                    <!-- 房贷专项功能 -->
                    <div v-if="loanData.loanType === 'house'" class="house-loan-section">
                      <el-form-item label="贷款方式">
                        <el-select
                          v-model="loanData.houseLoanMethod"
                          placeholder="请选择贷款方式"
                          style="width: 100%"
                          @change="handleHouseLoanMethodChange"
                        >
                          <el-option value="provident" label="公积金贷款">
                            <div>
                              <span>公积金贷款</span>
                              <div style="font-size: 12px; color: #8492a6;">利率较低，额度有限</div>
                            </div>
                          </el-option>
                          <el-option value="commercial" label="商业贷款">
                            <div>
                              <span>商业贷款</span>
                              <div style="font-size: 12px; color: #8492a6;">额度较高，利率相对较高</div>
                            </div>
                          </el-option>
                          <el-option value="combination" label="组合贷款">
                            <div>
                              <span>组合贷款</span>
                              <div style="font-size: 12px; color: #8492a6;">公积金+商业贷款组合</div>
                            </div>
                          </el-option>
                        </el-select>
                      </el-form-item>

                      <!-- 组合贷款特殊配置 -->
                      <div v-if="loanData.houseLoanMethod === 'combination'" class="combination-loan-config">
                        <el-form-item label="公积金贷款金额 (万元)">
                          <el-input-number
                            v-model="loanData.providentAmount"
                            :precision="2"
                            :step="1"
                            :min="0"
                            :max="loanData.principal"
                            placeholder="公积金贷款金额"
                            style="width: 100%"
                            @change="calculateLoan"
                          />
                          <div class="form-tip">剩余 {{ (loanData.principal - loanData.providentAmount).toFixed(2) }} 万元为商业贷款</div>
                        </el-form-item>
                      </div>
                    </div>

                    <el-form-item label="贷款金额 (万元)">
                      <el-input-number
                        v-model="loanData.principal"
                        :precision="2"
                        :step="1"
                        :min="1"
                        :max="10000"
                        placeholder="请输入贷款金额"
                        style="width: 100%"
                        @change="calculateLoan"
                      />
                    </el-form-item>

                    <!-- 利率输入区域 -->
                    <div v-if="loanData.houseLoanMethod === 'combination'" class="combination-rates">
                      <el-form-item label="公积金贷款利率 (%)">
                        <el-input-number
                          v-model="loanData.providentRate"
                          :precision="2"
                          :step="0.01"
                          :min="0.01"
                          :max="30"
                          placeholder="公积金贷款利率"
                          style="width: 100%"
                          @change="calculateLoan"
                        />
                        <div class="rate-tip">当前基准利率：3.25%</div>
                      </el-form-item>

                      <el-form-item label="商业贷款利率 (%)">
                        <el-input-number
                          v-model="loanData.commercialRate"
                          :precision="2"
                          :step="0.01"
                          :min="0.01"
                          :max="30"
                          placeholder="商业贷款利率"
                          style="width: 100%"
                          @change="calculateLoan"
                        />
                        <div class="rate-tip">当前基准利率：4.9%</div>
                      </el-form-item>
                    </div>

                    <el-form-item v-else label="年利率 (%)">
                      <el-input-number
                        v-model="loanData.annualRate"
                        :precision="2"
                        :step="0.01"
                        :min="0.01"
                        :max="30"
                        placeholder="请输入年利率"
                        style="width: 100%"
                        @change="calculateLoan"
                      />
                      <div class="rate-tip">{{ getRateTip() }}</div>
                    </el-form-item>

                    <el-form-item label="贷款期限">
                      <el-input-number
                        v-model="loanData.years"
                        :precision="0"
                        :step="1"
                        :min="1"
                        :max="50"
                        placeholder="请输入贷款年数"
                        style="width: 100%"
                        @change="calculateLoan"
                      />
                      <span style="margin-left: 8px;">年</span>
                    </el-form-item>

                    <el-form-item label="还款方式">
                      <el-radio-group v-model="loanData.paymentType" @change="calculateLoan">
                        <el-radio value="equal_payment">等额本息</el-radio>
                        <el-radio value="equal_principal">等额本金</el-radio>
                      </el-radio-group>
                      <div class="payment-type-tip">
                        <div v-if="loanData.paymentType === 'equal_payment'" class="tip-text">
                          等额本息：每月还款金额相同，前期利息占比较高
                        </div>
                        <div v-else class="tip-text">
                          等额本金：每月本金相同，总利息较少，前期还款压力大
                        </div>
                      </div>
                    </el-form-item>

                    <el-form-item v-if="loanData.loanType === 'house'" label="首付比例 (%)">
                      <el-input-number
                        v-model="loanData.downPaymentRatio"
                        :precision="1"
                        :step="1"
                        :min="0"
                        :max="90"
                        placeholder="首付比例"
                        style="width: 100%"
                        @change="calculateLoan"
                      />
                    </el-form-item>
                  </div>
                </el-col>

                <el-col :span="12">
                  <div class="result-section">
                    <h3 class="section-title">贷款结果</h3>

                    <!-- 加载状态 -->
                    <div v-if="isCalculating" class="loading-state">
                      <div class="loading-spinner-enhanced"></div>
                      <p>正在计算中...</p>
                    </div>

                    <div class="loan-result" v-if="loanResult">
                      <div class="payment-summary">
                        <div class="summary-item">
                          <div class="summary-label">月供金额</div>
                          <div class="summary-value primary">{{ formatCurrency(loanResult.monthlyPayment) }}</div>
                        </div>

                        <div class="summary-item">
                          <div class="summary-label">总利息</div>
                          <div class="summary-value">{{ formatCurrency(loanResult.totalInterest) }}</div>
                        </div>

                        <div class="summary-item">
                          <div class="summary-label">还款总额</div>
                          <div class="summary-value">{{ formatCurrency(loanResult.totalPayment) }}</div>
                        </div>
                      </div>

                      <div class="loan-details">
                        <h4>贷款详情</h4>
                        <el-descriptions :column="2" border>
                          <el-descriptions-item label="贷款本金">{{ formatCurrency(loanData.principal * 10000) }}</el-descriptions-item>
                          <el-descriptions-item v-if="!loanResult.isCombination" label="年利率">{{ loanData.annualRate }}%</el-descriptions-item>
                          <el-descriptions-item label="贷款期限">{{ loanData.years }}年 ({{ loanData.years * 12 }}期)</el-descriptions-item>
                          <el-descriptions-item label="还款方式">{{ loanData.paymentType === 'equal_payment' ? '等额本息' : '等额本金' }}</el-descriptions-item>
                          <el-descriptions-item v-if="!loanResult.isCombination" label="月利率">{{ (loanData.annualRate / 12).toFixed(4) }}%</el-descriptions-item>
                          <el-descriptions-item label="利息占比">{{ ((loanResult.totalInterest / loanResult.totalPayment) * 100).toFixed(2) }}%</el-descriptions-item>
                        </el-descriptions>

                        <!-- 组合贷款详细信息 -->
                        <div v-if="loanResult.isCombination" class="combination-details">
                          <h5>公积金贷款部分</h5>
                          <el-descriptions :column="2" border size="small">
                            <el-descriptions-item label="贷款金额">{{ formatCurrency(loanResult.providentPart.principal) }}</el-descriptions-item>
                            <el-descriptions-item label="年利率">{{ loanResult.providentPart.rate }}%</el-descriptions-item>
                            <el-descriptions-item label="月供">{{ formatCurrency(loanResult.providentPart.monthlyPayment) }}</el-descriptions-item>
                            <el-descriptions-item label="总利息">{{ formatCurrency(loanResult.providentPart.totalInterest) }}</el-descriptions-item>
                          </el-descriptions>

                          <h5 style="margin-top: 16px;">商业贷款部分</h5>
                          <el-descriptions :column="2" border size="small">
                            <el-descriptions-item label="贷款金额">{{ formatCurrency(loanResult.commercialPart.principal) }}</el-descriptions-item>
                            <el-descriptions-item label="年利率">{{ loanResult.commercialPart.rate }}%</el-descriptions-item>
                            <el-descriptions-item label="月供">{{ formatCurrency(loanResult.commercialPart.monthlyPayment) }}</el-descriptions-item>
                            <el-descriptions-item label="总利息">{{ formatCurrency(loanResult.commercialPart.totalInterest) }}</el-descriptions-item>
                          </el-descriptions>
                        </div>
                      </div>

                      <div class="payment-chart" v-if="loanResult.paymentSchedule">
                        <h4>还款计划表 (前12期)</h4>
                        <el-table :data="loanResult.paymentSchedule.slice(0, 12)" border max-height="300">
                          <el-table-column prop="period" label="期数" width="60" />
                          <el-table-column prop="payment" label="月供" width="100" />
                          <el-table-column v-if="loanResult.isCombination" prop="providentPayment" label="公积金" width="80" />
                          <el-table-column v-if="loanResult.isCombination" prop="commercialPayment" label="商业" width="80" />
                          <el-table-column prop="principal" label="本金" width="100" />
                          <el-table-column prop="interest" label="利息" width="100" />
                          <el-table-column prop="balance" label="余额" />
                        </el-table>
                      </div>

                      <div class="affordability-analysis">
                        <h4>负担能力分析</h4>
                        <div class="analysis-items">
                          <div class="analysis-item">
                            <span class="analysis-label">建议月收入：</span>
                            <span class="analysis-value">{{ formatCurrency(loanResult.monthlyPayment * 3) }}</span>
                            <span class="analysis-desc">(月供不超过收入1/3)</span>
                          </div>
                          <div class="analysis-item">
                            <span class="analysis-label">年收入要求：</span>
                            <span class="analysis-value">{{ formatCurrency(loanResult.monthlyPayment * 36) }}</span>
                            <span class="analysis-desc">(稳定收入来源)</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-else class="result-placeholder">
                      <el-icon class="placeholder-icon"><Money /></el-icon>
                      <p>贷款计算结果显示区域</p>
                      <p class="placeholder-tip">请输入贷款信息进行计算</p>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <el-tab-pane label="投资收益" name="investment">
            <div class="calculator-content">
              <el-row :gutter="30">
                <el-col :span="12">
                  <div class="input-section">
                    <h3 class="section-title">投资信息</h3>

                    <el-form-item label="初始投资 (万元)">
                      <el-input-number
                        v-model="investmentData.principal"
                        :precision="2"
                        :step="1"
                        :min="0.01"
                        :max="10000"
                        placeholder="请输入初始投资金额"
                        style="width: 100%"
                        @change="calculateInvestment"
                      />
                    </el-form-item>

                    <el-form-item label="年化收益率 (%)">
                      <el-input-number
                        v-model="investmentData.annualReturn"
                        :precision="2"
                        :step="0.1"
                        :min="-50"
                        :max="100"
                        placeholder="请输入年化收益率"
                        style="width: 100%"
                        @change="calculateInvestment"
                      />
                    </el-form-item>

                    <el-form-item label="投资期限 (年)">
                      <el-input-number
                        v-model="investmentData.years"
                        :precision="0"
                        :step="1"
                        :min="1"
                        :max="50"
                        placeholder="请输入投资年数"
                        style="width: 100%"
                        @change="calculateInvestment"
                      />
                    </el-form-item>

                    <el-form-item label="定期投入 (万元/年)">
                      <el-input-number
                        v-model="investmentData.regularInvestment"
                        :precision="2"
                        :step="0.1"
                        :min="0"
                        :max="1000"
                        placeholder="每年定期投入金额"
                        style="width: 100%"
                        @change="calculateInvestment"
                      />
                    </el-form-item>

                    <el-form-item label="复利频率">
                      <el-select v-model="investmentData.compoundFrequency" @change="calculateInvestment">
                        <el-option value="1" label="年复利">年复利</el-option>
                        <el-option value="2" label="半年复利">半年复利</el-option>
                        <el-option value="4" label="季度复利">季度复利</el-option>
                        <el-option value="12" label="月复利">月复利</el-option>
                        <el-option value="365" label="日复利">日复利</el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="通胀率 (%)">
                      <el-input-number
                        v-model="investmentData.inflationRate"
                        :precision="2"
                        :step="0.1"
                        :min="0"
                        :max="20"
                        placeholder="年通胀率"
                        style="width: 100%"
                        @change="calculateInvestment"
                      />
                    </el-form-item>

                    <div class="investment-types">
                      <h4>投资类型参考</h4>
                      <el-button-group>
                        <el-button size="small" @click="setInvestmentType('stock')">股票</el-button>
                        <el-button size="small" @click="setInvestmentType('bond')">债券</el-button>
                        <el-button size="small" @click="setInvestmentType('fund')">基金</el-button>
                        <el-button size="small" @click="setInvestmentType('deposit')">定期存款</el-button>
                      </el-button-group>
                    </div>
                  </div>
                </el-col>

                <el-col :span="12">
                  <div class="result-section">
                    <h3 class="section-title">投资收益结果</h3>

                    <div class="investment-result" v-if="investmentResult">
                      <div class="return-summary">
                        <div class="summary-item">
                          <div class="summary-label">最终价值</div>
                          <div class="summary-value primary">{{ formatCurrency(investmentResult.finalValue) }}</div>
                        </div>

                        <div class="summary-item">
                          <div class="summary-label">总收益</div>
                          <div class="summary-value positive">{{ formatCurrency(investmentResult.totalReturn) }}</div>
                        </div>

                        <div class="summary-item">
                          <div class="summary-label">收益率</div>
                          <div class="summary-value">{{ investmentResult.returnRate }}%</div>
                        </div>
                      </div>

                      <div class="investment-breakdown">
                        <h4>投资分析</h4>
                        <el-descriptions :column="2" border>
                          <el-descriptions-item label="初始投资">{{ formatCurrency(investmentData.principal * 10000) }}</el-descriptions-item>
                          <el-descriptions-item label="定期投入总额">{{ formatCurrency(investmentResult.totalRegularInvestment) }}</el-descriptions-item>
                          <el-descriptions-item label="投入本金总计">{{ formatCurrency(investmentResult.totalPrincipal) }}</el-descriptions-item>
                          <el-descriptions-item label="复利收益">{{ formatCurrency(investmentResult.compoundReturn) }}</el-descriptions-item>
                          <el-descriptions-item label="实际收益率">{{ investmentResult.realReturnRate }}%</el-descriptions-item>
                          <el-descriptions-item label="年化复合增长率">{{ investmentResult.cagr }}%</el-descriptions-item>
                        </el-descriptions>
                      </div>

                      <div class="yearly-projection">
                        <h4>年度投资增长 (前10年)</h4>
                        <el-table :data="investmentResult.yearlyProjection.slice(0, 10)" border max-height="300">
                          <el-table-column prop="year" label="年份" width="60" />
                          <el-table-column prop="investment" label="累计投入" />
                          <el-table-column prop="value" label="投资价值" />
                          <el-table-column prop="return" label="累计收益" />
                          <el-table-column prop="returnRate" label="收益率" width="80" />
                        </el-table>
                      </div>

                      <div class="risk-analysis">
                        <h4>风险分析</h4>
                        <div class="risk-scenarios">
                          <div class="scenario-item">
                            <span class="scenario-label">乐观情况 (+2%)：</span>
                            <span class="scenario-value">{{ formatCurrency(investmentResult.optimisticValue) }}</span>
                          </div>
                          <div class="scenario-item">
                            <span class="scenario-label">悲观情况 (-2%)：</span>
                            <span class="scenario-value">{{ formatCurrency(investmentResult.pessimisticValue) }}</span>
                          </div>
                          <div class="scenario-item">
                            <span class="scenario-label">通胀调整后价值：</span>
                            <span class="scenario-value">{{ formatCurrency(investmentResult.inflationAdjustedValue) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-else class="result-placeholder">
                      <el-icon class="placeholder-icon"><Money /></el-icon>
                      <p>投资收益计算结果显示区域</p>
                      <p class="placeholder-tip">请输入投资信息进行计算</p>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <el-tab-pane label="复利计算" name="compound">
            <div class="calculator-content">
              <el-row :gutter="30">
                <el-col :span="12">
                  <div class="input-section">
                    <h3 class="section-title">复利参数</h3>

                    <el-form-item label="本金 (万元)">
                      <el-input-number
                        v-model="compoundData.principal"
                        :precision="2"
                        :step="1"
                        :min="0.01"
                        :max="10000"
                        placeholder="请输入本金"
                        style="width: 100%"
                        @change="calculateCompound"
                      />
                    </el-form-item>

                    <el-form-item label="年利率 (%)">
                      <el-input-number
                        v-model="compoundData.rate"
                        :precision="2"
                        :step="0.1"
                        :min="0.01"
                        :max="50"
                        placeholder="请输入年利率"
                        style="width: 100%"
                        @change="calculateCompound"
                      />
                    </el-form-item>

                    <el-form-item label="时间 (年)">
                      <el-input-number
                        v-model="compoundData.time"
                        :precision="0"
                        :step="1"
                        :min="1"
                        :max="100"
                        placeholder="请输入时间"
                        style="width: 100%"
                        @change="calculateCompound"
                      />
                    </el-form-item>

                    <el-form-item label="复利频率">
                      <el-select v-model="compoundData.frequency" @change="calculateCompound">
                        <el-option :value="1" label="年复利 (1次/年)">年复利</el-option>
                        <el-option :value="2" label="半年复利 (2次/年)">半年复利</el-option>
                        <el-option :value="4" label="季度复利 (4次/年)">季度复利</el-option>
                        <el-option :value="12" label="月复利 (12次/年)">月复利</el-option>
                        <el-option :value="52" label="周复利 (52次/年)">周复利</el-option>
                        <el-option :value="365" label="日复利 (365次/年)">日复利</el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="定期追加 (万元)">
                      <el-input-number
                        v-model="compoundData.additionalPayment"
                        :precision="2"
                        :step="0.1"
                        :min="0"
                        :max="1000"
                        placeholder="定期追加金额"
                        style="width: 100%"
                        @change="calculateCompound"
                      />
                    </el-form-item>

                    <el-form-item label="追加频率">
                      <el-select v-model="compoundData.paymentFrequency" @change="calculateCompound">
                        <el-option :value="1" label="年度追加">年度追加</el-option>
                        <el-option :value="12" label="月度追加">月度追加</el-option>
                        <el-option :value="4" label="季度追加">季度追加</el-option>
                      </el-select>
                    </el-form-item>

                    <div class="compound-examples">
                      <h4>复利示例</h4>
                      <el-button-group>
                        <el-button size="small" @click="setCompoundExample('retirement')">退休储蓄</el-button>
                        <el-button size="small" @click="setCompoundExample('education')">教育基金</el-button>
                        <el-button size="small" @click="setCompoundExample('emergency')">应急基金</el-button>
                      </el-button-group>
                    </div>
                  </div>
                </el-col>

                <el-col :span="12">
                  <div class="result-section">
                    <h3 class="section-title">复利计算结果</h3>

                    <div class="compound-result" v-if="compoundResult">
                      <div class="compound-summary">
                        <div class="summary-item">
                          <div class="summary-label">最终金额</div>
                          <div class="summary-value primary">{{ formatCurrency(compoundResult.finalAmount) }}</div>
                        </div>

                        <div class="summary-item">
                          <div class="summary-label">复利收益</div>
                          <div class="summary-value positive">{{ formatCurrency(compoundResult.compoundInterest) }}</div>
                        </div>

                        <div class="summary-item">
                          <div class="summary-label">收益倍数</div>
                          <div class="summary-value">{{ compoundResult.multiplier }}倍</div>
                        </div>
                      </div>

                      <div class="compound-breakdown">
                        <h4>复利分析</h4>
                        <el-descriptions :column="2" border>
                          <el-descriptions-item label="初始本金">{{ formatCurrency(compoundData.principal * 10000) }}</el-descriptions-item>
                          <el-descriptions-item label="追加本金总计">{{ formatCurrency(compoundResult.totalAdditional) }}</el-descriptions-item>
                          <el-descriptions-item label="本金合计">{{ formatCurrency(compoundResult.totalPrincipal) }}</el-descriptions-item>
                          <el-descriptions-item label="单利收益">{{ formatCurrency(compoundResult.simpleInterest) }}</el-descriptions-item>
                          <el-descriptions-item label="复利优势">{{ formatCurrency(compoundResult.compoundAdvantage) }}</el-descriptions-item>
                          <el-descriptions-item label="有效年利率">{{ compoundResult.effectiveRate }}%</el-descriptions-item>
                        </el-descriptions>
                      </div>

                      <div class="frequency-comparison">
                        <h4>不同复利频率对比</h4>
                        <el-table :data="compoundResult.frequencyComparison" border>
                          <el-table-column prop="frequency" label="复利频率" />
                          <el-table-column prop="finalAmount" label="最终金额" />
                          <el-table-column prop="interest" label="利息收益" />
                          <el-table-column prop="effectiveRate" label="有效利率" />
                        </el-table>
                      </div>

                      <div class="time-value-analysis">
                        <h4>时间价值分析</h4>
                        <div class="time-scenarios">
                          <div class="scenario-item">
                            <span class="scenario-label">提前5年开始：</span>
                            <span class="scenario-value">{{ formatCurrency(compoundResult.earlyStartValue) }}</span>
                            <span class="scenario-benefit">多获得 {{ formatCurrency(compoundResult.earlyStartBenefit) }}</span>
                          </div>
                          <div class="scenario-item">
                            <span class="scenario-label">延迟5年开始：</span>
                            <span class="scenario-value">{{ formatCurrency(compoundResult.lateStartValue) }}</span>
                            <span class="scenario-loss">少获得 {{ formatCurrency(compoundResult.lateStartLoss) }}</span>
                          </div>
                        </div>
                      </div>

                      <div class="compound-chart-data">
                        <h4>年度增长明细 (每5年)</h4>
                        <el-table :data="compoundResult.yearlyGrowth" border max-height="250">
                          <el-table-column prop="year" label="年份" width="60" />
                          <el-table-column prop="principal" label="累计本金" />
                          <el-table-column prop="amount" label="账户余额" />
                          <el-table-column prop="interest" label="累计利息" />
                          <el-table-column prop="growth" label="年增长率" width="80" />
                        </el-table>
                      </div>
                    </div>

                    <div v-else class="result-placeholder">
                      <el-icon class="placeholder-icon"><Money /></el-icon>
                      <p>复利计算结果显示区域</p>
                      <p class="placeholder-tip">请输入复利参数进行计算</p>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button type="primary" @click="saveCalculation">保存计算</el-button>
        <el-button @click="exportResults">导出结果</el-button>
        <el-button @click="compareScenarios">方案对比</el-button>
        <el-button @click="clearAll">清空所有</el-button>
      </div>

      <!-- 处理结果提示 -->
      <div v-if="processResult" class="result-alert">
        <el-alert
          :title="processResult.message"
          :type="processResult.success ? 'success' : 'error'"
          :closable="false"
        />
      </div>
    </el-card>

    <!-- 使用说明 -->
    <el-card class="help-card">
      <template #header>
        <span>财务计算说明</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <h4>贷款计算</h4>
          <ul class="help-list">
            <li><strong>等额本息</strong>：每月还款金额相同</li>
            <li><strong>等额本金</strong>：每月本金相同，利息递减</li>
            <li><strong>月供比例</strong>：建议不超过收入的1/3</li>
            <li><strong>提前还款</strong>：可节省利息支出</li>
          </ul>
        </el-col>
        <el-col :span="6">
          <h4>投资收益</h4>
          <ul class="help-list">
            <li><strong>年化收益率</strong>：投资的年平均回报率</li>
            <li><strong>复合增长率</strong>：考虑复利的实际增长率</li>
            <li><strong>风险评估</strong>：高收益通常伴随高风险</li>
            <li><strong>分散投资</strong>：降低投资组合风险</li>
          </ul>
        </el-col>
        <el-col :span="6">
          <h4>复利效应</h4>
          <ul class="help-list">
            <li><strong>时间价值</strong>：时间是复利的最好朋友</li>
            <li><strong>复利频率</strong>：频率越高，收益越大</li>
            <li><strong>定期投入</strong>：平均成本法降低风险</li>
            <li><strong>长期坚持</strong>：复利效应需要时间积累</li>
          </ul>
        </el-col>
        <el-col :span="6">
          <h4>理财建议</h4>
          <ul class="help-list">
            <li><strong>应急基金</strong>：准备3-6个月生活费</li>
            <li><strong>债务管理</strong>：优先偿还高利率债务</li>
            <li><strong>投资规划</strong>：根据风险承受能力选择</li>
            <li><strong>定期评估</strong>：调整投资策略和目标</li>
          </ul>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Money } from '@element-plus/icons-vue'

// 响应式数据
const activeCalculator = ref('loan')
const processResult = ref<{ success: boolean; message: string } | null>(null)

// 贷款计算数据
const loanData = reactive({
  principal: 100, // 万元
  annualRate: 4.9, // 年利率 %
  years: 30, // 贷款年数
  paymentType: 'equal_payment', // 还款方式
  downPaymentRatio: 30, // 首付比例 %
  loanType: 'house', // 贷款种类
  houseLoanMethod: 'commercial', // 房贷方式
  providentAmount: 50, // 公积金贷款金额
  providentRate: 3.25, // 公积金贷款利率
  commercialRate: 4.9 // 商业贷款利率
})

const loanResult = ref<{
  monthlyPayment: number;
  totalPayment: number;
  totalInterest: number;
  paymentSchedule: any[];
  isCombination?: boolean;
  providentPart?: any;
  commercialPart?: any;
} | null>(null)

// 投资收益数据
const investmentData = reactive({
  principal: 10, // 万元
  annualReturn: 8, // 年化收益率 %
  years: 10, // 投资年数
  regularInvestment: 2, // 定期投入 万元/年
  compoundFrequency: 12, // 复利频率
  inflationRate: 3 // 通胀率 %
})

const investmentResult = ref<any>(null)

// 复利计算数据
const compoundData = reactive({
  principal: 10, // 万元
  rate: 6, // 年利率 %
  time: 20, // 时间 年
  frequency: 12, // 复利频率
  additionalPayment: 1, // 定期追加 万元
  paymentFrequency: 12 // 追加频率
})

const compoundResult = ref<any>(null)

// 加载状态
const isCalculating = ref(false)

// 方法
const handleCalculatorChange = (tabName: string) => {
  processResult.value = null

  // 根据切换的计算器类型执行相应计算
  switch (tabName) {
    case 'loan':
      calculateLoan()
      break
    case 'investment':
      calculateInvestment()
      break
    case 'compound':
      calculateCompound()
      break
  }
}

// 处理贷款类型变化
const handleLoanTypeChange = () => {
  // 根据贷款类型设置默认参数
  switch (loanData.loanType) {
    case 'house':
      loanData.principal = 200
      loanData.annualRate = 4.9
      loanData.years = 30
      loanData.downPaymentRatio = 30
      loanData.houseLoanMethod = 'commercial'
      break
    case 'car':
      loanData.principal = 20
      loanData.annualRate = 6.5
      loanData.years = 5
      loanData.downPaymentRatio = 20
      break
    case 'business':
      loanData.principal = 50
      loanData.annualRate = 7.2
      loanData.years = 10
      loanData.downPaymentRatio = 0
      break
    case 'personal':
      loanData.principal = 10
      loanData.annualRate = 12.0
      loanData.years = 3
      loanData.downPaymentRatio = 0
      break
  }
  calculateLoan()
}

// 处理房贷方式变化
const handleHouseLoanMethodChange = () => {
  // 根据房贷方式设置对应的基准利率
  switch (loanData.houseLoanMethod) {
    case 'provident':
      loanData.annualRate = 3.25 // 公积金贷款基准利率
      break
    case 'commercial':
      loanData.annualRate = 4.9 // 商业贷款基准利率
      break
    case 'combination':
      loanData.providentRate = 3.25
      loanData.commercialRate = 4.9
      loanData.providentAmount = Math.min(50, loanData.principal * 0.5) // 默认公积金贷款50万或总额的50%
      break
  }
  calculateLoan()
}

// 获取利率提示信息
const getRateTip = (): string => {
  if (loanData.loanType === 'house') {
    switch (loanData.houseLoanMethod) {
      case 'provident':
        return '公积金贷款基准利率：3.25%（5年以上）'
      case 'commercial':
        return '商业贷款基准利率：4.9%（5年以上首套房）'
      default:
        return '当前基准利率：4.9%'
    }
  } else {
    switch (loanData.loanType) {
      case 'car':
        return '汽车贷款参考利率：6.5%'
      case 'business':
        return '经营贷款参考利率：7.2%'
      case 'personal':
        return '个人贷款参考利率：12.0%'
      default:
        return '请输入年利率'
    }
  }
}

const calculateLoan = async () => {
  if (!loanData.principal || !loanData.years) {
    loanResult.value = null
    return
  }

  // 添加加载状态
  isCalculating.value = true

  try {
    // 模拟计算延迟以显示加载效果
    await new Promise(resolve => setTimeout(resolve, 300))

  // 组合贷款特殊处理
  if (loanData.loanType === 'house' && loanData.houseLoanMethod === 'combination') {
    calculateCombinationLoan()
    return
  }

  // 检查利率
  if (!loanData.annualRate) {
    loanResult.value = null
    return
  }

  const principal = loanData.principal * 10000 // 转换为元
  const monthlyRate = loanData.annualRate / 100 / 12 // 月利率
  const totalMonths = loanData.years * 12 // 总月数

  let monthlyPayment = 0
  let totalPayment = 0
  let totalInterest = 0
  const paymentSchedule = []

  if (loanData.paymentType === 'equal_payment') {
    // 等额本息
    monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, totalMonths)) /
                    (Math.pow(1 + monthlyRate, totalMonths) - 1)
    totalPayment = monthlyPayment * totalMonths
    totalInterest = totalPayment - principal

    // 生成还款计划表
    let remainingPrincipal = principal
    for (let i = 1; i <= totalMonths; i++) {
      const interestPayment = remainingPrincipal * monthlyRate
      const principalPayment = monthlyPayment - interestPayment
      remainingPrincipal -= principalPayment

      paymentSchedule.push({
        period: i,
        payment: monthlyPayment.toFixed(2),
        principal: principalPayment.toFixed(2),
        interest: interestPayment.toFixed(2),
        balance: Math.max(0, remainingPrincipal).toFixed(2)
      })
    }
  } else {
    // 等额本金
    const monthlyPrincipal = principal / totalMonths
    totalPayment = 0

    let remainingPrincipal = principal
    for (let i = 1; i <= totalMonths; i++) {
      const interestPayment = remainingPrincipal * monthlyRate
      const payment = monthlyPrincipal + interestPayment
      remainingPrincipal -= monthlyPrincipal
      totalPayment += payment

      paymentSchedule.push({
        period: i,
        payment: payment.toFixed(2),
        principal: monthlyPrincipal.toFixed(2),
        interest: interestPayment.toFixed(2),
        balance: Math.max(0, remainingPrincipal).toFixed(2)
      })
    }

    monthlyPayment = parseFloat(paymentSchedule[0].payment) // 首月月供
    totalInterest = totalPayment - principal
  }

  loanResult.value = {
    monthlyPayment,
    totalPayment,
    totalInterest,
    paymentSchedule
  }

  } catch (error) {
    console.error('计算出错:', error)
    ElMessage.error('计算过程中出现错误，请检查输入数据')
  } finally {
    isCalculating.value = false
  }
}

// 组合贷款计算
const calculateCombinationLoan = () => {
  if (!loanData.providentAmount || !loanData.providentRate || !loanData.commercialRate) {
    loanResult.value = null
    return
  }

  const providentPrincipal = loanData.providentAmount * 10000 // 公积金贷款本金
  const commercialPrincipal = (loanData.principal - loanData.providentAmount) * 10000 // 商业贷款本金

  if (commercialPrincipal < 0) {
    loanResult.value = null
    return
  }

  const providentMonthlyRate = loanData.providentRate / 100 / 12 // 公积金月利率
  const commercialMonthlyRate = loanData.commercialRate / 100 / 12 // 商业贷款月利率
  const totalMonths = loanData.years * 12 // 总月数

  // 计算公积金贷款部分
  let providentMonthlyPayment = 0
  let providentTotalPayment = 0
  let providentTotalInterest = 0

  if (providentPrincipal > 0) {
    if (loanData.paymentType === 'equal_payment') {
      providentMonthlyPayment = providentPrincipal * (providentMonthlyRate * Math.pow(1 + providentMonthlyRate, totalMonths)) /
                               (Math.pow(1 + providentMonthlyRate, totalMonths) - 1)
    } else {
      providentMonthlyPayment = providentPrincipal / totalMonths + providentPrincipal * providentMonthlyRate
    }
    providentTotalPayment = loanData.paymentType === 'equal_payment' ?
                           providentMonthlyPayment * totalMonths :
                           providentPrincipal + (providentPrincipal * providentMonthlyRate * (totalMonths + 1) / 2)
    providentTotalInterest = providentTotalPayment - providentPrincipal
  }

  // 计算商业贷款部分
  let commercialMonthlyPayment = 0
  let commercialTotalPayment = 0
  let commercialTotalInterest = 0

  if (commercialPrincipal > 0) {
    if (loanData.paymentType === 'equal_payment') {
      commercialMonthlyPayment = commercialPrincipal * (commercialMonthlyRate * Math.pow(1 + commercialMonthlyRate, totalMonths)) /
                                (Math.pow(1 + commercialMonthlyRate, totalMonths) - 1)
    } else {
      commercialMonthlyPayment = commercialPrincipal / totalMonths + commercialPrincipal * commercialMonthlyRate
    }
    commercialTotalPayment = loanData.paymentType === 'equal_payment' ?
                            commercialMonthlyPayment * totalMonths :
                            commercialPrincipal + (commercialPrincipal * commercialMonthlyRate * (totalMonths + 1) / 2)
    commercialTotalInterest = commercialTotalPayment - commercialPrincipal
  }

  // 合计
  const monthlyPayment = providentMonthlyPayment + commercialMonthlyPayment
  const totalPayment = providentTotalPayment + commercialTotalPayment
  const totalInterest = providentTotalInterest + commercialTotalInterest

  // 生成组合还款计划表（简化版，显示前12期）
  const paymentSchedule = []
  let providentBalance = providentPrincipal
  let commercialBalance = commercialPrincipal

  for (let i = 1; i <= Math.min(12, totalMonths); i++) {
    let providentInterest = 0
    let providentPrincipalPayment = 0
    let commercialInterest = 0
    let commercialPrincipalPayment = 0

    if (providentBalance > 0) {
      providentInterest = providentBalance * providentMonthlyRate
      if (loanData.paymentType === 'equal_payment') {
        providentPrincipalPayment = providentMonthlyPayment - providentInterest
      } else {
        providentPrincipalPayment = providentPrincipal / totalMonths
      }
      providentBalance -= providentPrincipalPayment
    }

    if (commercialBalance > 0) {
      commercialInterest = commercialBalance * commercialMonthlyRate
      if (loanData.paymentType === 'equal_payment') {
        commercialPrincipalPayment = commercialMonthlyPayment - commercialInterest
      } else {
        commercialPrincipalPayment = commercialPrincipal / totalMonths
      }
      commercialBalance -= commercialPrincipalPayment
    }

    paymentSchedule.push({
      period: i,
      payment: monthlyPayment.toFixed(2),
      principal: (providentPrincipalPayment + commercialPrincipalPayment).toFixed(2),
      interest: (providentInterest + commercialInterest).toFixed(2),
      balance: Math.max(0, providentBalance + commercialBalance).toFixed(2),
      providentPayment: providentMonthlyPayment.toFixed(2),
      commercialPayment: commercialMonthlyPayment.toFixed(2)
    })
  }

  loanResult.value = {
    monthlyPayment,
    totalPayment,
    totalInterest,
    paymentSchedule,
    isCombination: true,
    providentPart: {
      principal: providentPrincipal,
      monthlyPayment: providentMonthlyPayment,
      totalPayment: providentTotalPayment,
      totalInterest: providentTotalInterest,
      rate: loanData.providentRate
    },
    commercialPart: {
      principal: commercialPrincipal,
      monthlyPayment: commercialMonthlyPayment,
      totalPayment: commercialTotalPayment,
      totalInterest: commercialTotalInterest,
      rate: loanData.commercialRate
    }
  }
}

const calculateInvestment = async () => {
  if (!investmentData.principal || !investmentData.annualReturn || !investmentData.years) {
    investmentResult.value = null
    return
  }

  isCalculating.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 200))

  const principal = investmentData.principal * 10000 // 转换为元
  const rate = investmentData.annualReturn / 100
  const years = investmentData.years
  const regularInvestment = investmentData.regularInvestment * 10000 // 转换为元
  const compoundFreq = investmentData.compoundFrequency
  const inflationRate = investmentData.inflationRate / 100

  // 计算复利
  const periodicRate = rate / compoundFreq
  const totalPeriods = years * compoundFreq

  // 初始投资的复利增长
  const initialGrowth = principal * Math.pow(1 + periodicRate, totalPeriods)

  // 定期投资的复利增长（年金现值公式）
  let regularGrowth = 0
  if (regularInvestment > 0) {
    const periodicPayment = regularInvestment / (12 / compoundFreq) // 转换为复利频率对应的投资额
    regularGrowth = periodicPayment * ((Math.pow(1 + periodicRate, totalPeriods) - 1) / periodicRate)
  }

  const finalValue = initialGrowth + regularGrowth
  const totalRegularInvestment = regularInvestment * years
  const totalPrincipal = principal + totalRegularInvestment
  const totalReturn = finalValue - totalPrincipal
  const returnRate = ((totalReturn / totalPrincipal) * 100).toFixed(2)
  const compoundReturn = finalValue - principal - totalRegularInvestment
  const realReturnRate = (((finalValue / totalPrincipal) - 1 - inflationRate) * 100).toFixed(2)
  const cagr = ((Math.pow(finalValue / totalPrincipal, 1 / years) - 1) * 100).toFixed(2)

  // 年度投资增长
  const yearlyProjection = []
  for (let year = 1; year <= years; year++) {
    const yearPeriods = year * compoundFreq
    const yearInitialGrowth = principal * Math.pow(1 + periodicRate, yearPeriods)

    let yearRegularGrowth = 0
    if (regularInvestment > 0) {
      const periodicPayment = regularInvestment / (12 / compoundFreq)
      yearRegularGrowth = periodicPayment * ((Math.pow(1 + periodicRate, yearPeriods) - 1) / periodicRate)
    }

    const yearValue = yearInitialGrowth + yearRegularGrowth
    const yearInvestment = principal + (regularInvestment * year)
    const yearReturn = yearValue - yearInvestment
    const yearReturnRate = ((yearReturn / yearInvestment) * 100).toFixed(1)

    yearlyProjection.push({
      year,
      investment: formatCurrency(yearInvestment),
      value: formatCurrency(yearValue),
      return: formatCurrency(yearReturn),
      returnRate: yearReturnRate + '%'
    })
  }

  // 风险分析
  const optimisticRate = rate + 0.02
  const pessimisticRate = Math.max(0, rate - 0.02)

  const optimisticValue = principal * Math.pow(1 + optimisticRate / compoundFreq, totalPeriods) +
    (regularInvestment > 0 ? (regularInvestment / (12 / compoundFreq)) *
    ((Math.pow(1 + optimisticRate / compoundFreq, totalPeriods) - 1) / (optimisticRate / compoundFreq)) : 0)

  const pessimisticValue = principal * Math.pow(1 + pessimisticRate / compoundFreq, totalPeriods) +
    (regularInvestment > 0 ? (regularInvestment / (12 / compoundFreq)) *
    ((Math.pow(1 + pessimisticRate / compoundFreq, totalPeriods) - 1) / (pessimisticRate / compoundFreq)) : 0)

  const inflationAdjustedValue = finalValue / Math.pow(1 + inflationRate, years)

  investmentResult.value = {
    finalValue,
    totalReturn,
    returnRate,
    totalRegularInvestment,
    totalPrincipal,
    compoundReturn,
    realReturnRate,
    cagr,
    yearlyProjection,
    optimisticValue,
    pessimisticValue,
    inflationAdjustedValue
  }

  } catch (error) {
    console.error('投资计算出错:', error)
    ElMessage.error('投资计算过程中出现错误，请检查输入数据')
  } finally {
    isCalculating.value = false
  }
}

const calculateCompound = async () => {
  if (!compoundData.principal || !compoundData.rate || !compoundData.time) {
    compoundResult.value = null
    return
  }

  isCalculating.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 200))

  const principal = compoundData.principal * 10000 // 转换为元
  const rate = compoundData.rate / 100
  const time = compoundData.time
  const frequency = compoundData.frequency
  const additionalPayment = compoundData.additionalPayment * 10000 // 转换为元
  const paymentFreq = compoundData.paymentFrequency

  // 复利计算
  const periodicRate = rate / frequency
  const totalPeriods = time * frequency

  // 初始本金的复利增长
  const principalGrowth = principal * Math.pow(1 + periodicRate, totalPeriods)

  // 定期追加的复利增长
  let additionalGrowth = 0
  if (additionalPayment > 0) {
    const periodicPayment = additionalPayment / (12 / paymentFreq) // 转换为复利频率对应的追加额
    additionalGrowth = periodicPayment * ((Math.pow(1 + periodicRate, totalPeriods) - 1) / periodicRate) * (frequency / paymentFreq)
  }

  const finalAmount = principalGrowth + additionalGrowth
  const totalAdditional = additionalPayment * time * (paymentFreq / 12)
  const totalPrincipal = principal + totalAdditional
  const compoundInterest = finalAmount - totalPrincipal
  const multiplier = (finalAmount / totalPrincipal).toFixed(2)

  // 单利计算对比
  const simpleInterest = totalPrincipal * rate * time
  const compoundAdvantage = compoundInterest - simpleInterest

  // 有效年利率
  const effectiveRate = ((Math.pow(1 + rate / frequency, frequency) - 1) * 100).toFixed(4)

  // 不同复利频率对比
  const frequencies = [
    { name: '年复利', value: 1 },
    { name: '半年复利', value: 2 },
    { name: '季度复利', value: 4 },
    { name: '月复利', value: 12 },
    { name: '日复利', value: 365 }
  ]

  const frequencyComparison = frequencies.map(freq => {
    const freqRate = rate / freq.value
    const freqPeriods = time * freq.value
    const freqAmount = principal * Math.pow(1 + freqRate, freqPeriods)
    const freqInterest = freqAmount - principal
    const freqEffectiveRate = ((Math.pow(1 + rate / freq.value, freq.value) - 1) * 100).toFixed(4)

    return {
      frequency: freq.name,
      finalAmount: formatCurrency(freqAmount),
      interest: formatCurrency(freqInterest),
      effectiveRate: freqEffectiveRate + '%'
    }
  })

  // 时间价值分析
  const earlyStartTime = time + 5
  const earlyStartAmount = principal * Math.pow(1 + periodicRate, earlyStartTime * frequency)
  const earlyStartBenefit = earlyStartAmount - finalAmount

  const lateStartTime = Math.max(1, time - 5)
  const lateStartAmount = principal * Math.pow(1 + periodicRate, lateStartTime * frequency)
  const lateStartLoss = finalAmount - lateStartAmount

  // 年度增长明细（每5年显示一次）
  const yearlyGrowth = []
  for (let year = 5; year <= time; year += 5) {
    const yearPeriods = year * frequency
    const yearAmount = principal * Math.pow(1 + periodicRate, yearPeriods)
    const yearAdditional = additionalPayment * year * (paymentFreq / 12)
    const yearPrincipal = principal + yearAdditional
    const yearInterest = yearAmount - principal
    const yearGrowthRate = year > 5 ?
      (((yearAmount / (principal * Math.pow(1 + periodicRate, (year - 5) * frequency))) - 1) * 100).toFixed(2) :
      ((rate * 100).toFixed(2))

    yearlyGrowth.push({
      year,
      principal: formatCurrency(yearPrincipal),
      amount: formatCurrency(yearAmount),
      interest: formatCurrency(yearInterest),
      growth: yearGrowthRate + '%'
    })
  }

  compoundResult.value = {
    finalAmount,
    compoundInterest,
    multiplier,
    totalAdditional,
    totalPrincipal,
    simpleInterest,
    compoundAdvantage,
    effectiveRate,
    frequencyComparison,
    earlyStartValue: earlyStartAmount,
    earlyStartBenefit,
    lateStartValue: lateStartAmount,
    lateStartLoss,
    yearlyGrowth
  }

  } catch (error) {
    console.error('复利计算出错:', error)
    ElMessage.error('复利计算过程中出现错误，请检查输入数据')
  } finally {
    isCalculating.value = false
  }
}

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}



const setInvestmentType = (type: string) => {
  switch (type) {
    case 'stock':
      investmentData.annualReturn = 10
      investmentData.compoundFrequency = 1
      break
    case 'bond':
      investmentData.annualReturn = 4
      investmentData.compoundFrequency = 2
      break
    case 'fund':
      investmentData.annualReturn = 8
      investmentData.compoundFrequency = 12
      break
    case 'deposit':
      investmentData.annualReturn = 2.5
      investmentData.compoundFrequency = 4
      break
  }
  calculateInvestment()
}

const setCompoundExample = (type: string) => {
  switch (type) {
    case 'retirement':
      compoundData.principal = 5
      compoundData.rate = 7
      compoundData.time = 30
      compoundData.additionalPayment = 2
      compoundData.paymentFrequency = 12
      break
    case 'education':
      compoundData.principal = 10
      compoundData.rate = 6
      compoundData.time = 18
      compoundData.additionalPayment = 1
      compoundData.paymentFrequency = 12
      break
    case 'emergency':
      compoundData.principal = 2
      compoundData.rate = 3
      compoundData.time = 5
      compoundData.additionalPayment = 0.5
      compoundData.paymentFrequency = 12
      break
  }
  calculateCompound()
}

const saveCalculation = () => {
  // 保存当前计算结果
  ElMessage.success('计算结果已保存')
}

const exportResults = () => {
  // 导出计算结果
  const results = {
    calculator: activeCalculator.value,
    timestamp: new Date().toLocaleString(),
    loan: loanResult.value,
    investment: investmentResult.value,
    compound: compoundResult.value
  }

  const dataStr = JSON.stringify(results, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `finance-calculator-results-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('计算结果已导出')
}

const compareScenarios = () => {
  // 方案对比功能
  ElMessage.info('方案对比功能开发中')
}

const clearAll = () => {
  // 清空所有数据
  loanResult.value = null
  investmentResult.value = null
  compoundResult.value = null
  processResult.value = null

  ElMessage.info('所有数据已清空')
}

// 初始化
onMounted(() => {
  calculateLoan()
  calculateInvestment()
  calculateCompound()
})
</script>

<style scoped>
.finance-calculator-container {
  max-width: 1400px;
  margin: 0 auto;
}

.tool-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 20px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
}

.calculator-tabs {
  margin-bottom: 30px;
}

.calculator-content {
  margin-top: 20px;
}

.section-title {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
  margin: 0 0 20px 0;
}

.input-section,
.result-section {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
  height: 100%;
}

.quick-presets {
  margin: 20px 0;
}

.quick-presets h4 {
  margin: 0 0 12px 0;
  font-weight: 600;
  color: #303133;
}

.investment-types,
.compound-examples {
  margin: 20px 0;
}

.investment-types h4,
.compound-examples h4 {
  margin: 0 0 12px 0;
  font-weight: 600;
  color: #303133;
}

/* 贷款结果样式 */
.loan-result {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.payment-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.summary-item {
  flex: 1;
  text-align: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.summary-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.summary-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.summary-value.primary {
  color: #409eff;
  font-size: 24px;
}

.summary-value.positive {
  color: #67c23a;
}

.loan-details,
.payment-chart,
.affordability-analysis {
  margin: 20px 0;
}

.loan-details h4,
.payment-chart h4,
.affordability-analysis h4 {
  margin: 0 0 12px 0;
  font-weight: 600;
  color: #303133;
}

.analysis-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.analysis-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.analysis-label {
  font-weight: 600;
  color: #303133;
}

.analysis-value {
  font-weight: 600;
  color: #409eff;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.analysis-desc {
  font-size: 12px;
  color: #909399;
}

/* 投资结果样式 */
.investment-result {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.return-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.investment-breakdown,
.yearly-projection,
.risk-analysis {
  margin: 20px 0;
}

.investment-breakdown h4,
.yearly-projection h4,
.risk-analysis h4 {
  margin: 0 0 12px 0;
  font-weight: 600;
  color: #303133;
}

.risk-scenarios {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.scenario-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.scenario-label {
  font-weight: 600;
  color: #303133;
}

.scenario-value {
  font-weight: 600;
  color: #409eff;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* 复利结果样式 */
.compound-result {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.compound-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.compound-breakdown,
.frequency-comparison,
.time-value-analysis,
.compound-chart-data {
  margin: 20px 0;
}

.compound-breakdown h4,
.frequency-comparison h4,
.time-value-analysis h4,
.compound-chart-data h4 {
  margin: 0 0 12px 0;
  font-weight: 600;
  color: #303133;
}

.time-scenarios {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.scenario-benefit {
  font-size: 12px;
  color: #67c23a;
  font-weight: 600;
}

.scenario-loss {
  font-size: 12px;
  color: #f56c6c;
  font-weight: 600;
}

.result-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.placeholder-tip {
  font-size: 12px;
  margin-top: 8px;
}

.action-section {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.result-alert {
  margin-top: 20px;
}

.help-card {
  margin-top: 20px;
}

.help-list {
  margin: 0;
  padding-left: 20px;
}

.help-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

/* 房贷专项功能样式 */
.house-loan-section {
  margin: 16px 0;
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.combination-loan-config {
  margin-top: 16px;
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.combination-rates {
  margin: 16px 0;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.rate-tip {
  font-size: 12px;
  color: #67c23a;
  margin-top: 4px;
  font-weight: 500;
}

.payment-type-tip {
  margin-top: 8px;
}

.tip-text {
  font-size: 12px;
  color: #606266;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.combination-details {
  margin-top: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.combination-details h5 {
  margin: 0 0 12px 0;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calculator-content .el-row {
    flex-direction: column;
  }

  .calculator-content .el-col {
    margin-bottom: 20px;
  }

  .payment-summary,
  .return-summary,
  .compound-summary {
    flex-direction: column;
  }

  .analysis-items,
  .risk-scenarios,
  .time-scenarios {
    gap: 8px;
  }

  .analysis-item,
  .scenario-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .summary-value.primary {
    font-size: 20px;
  }

  .house-loan-section {
    margin: 12px 0;
    padding: 12px;
  }

  .combination-loan-config {
    margin-top: 12px;
    padding: 8px;
  }

  .combination-details {
    margin-top: 16px;
    padding: 12px;
  }
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  background: var(--color-background-soft);
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border);
  margin-bottom: var(--spacing-4);
}

.loading-state p {
  margin-top: var(--spacing-3);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}
</style>