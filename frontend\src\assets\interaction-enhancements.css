/* Interactive Experience Enhancements */

/* Enhanced Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.loading-spinner-enhanced {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: relative;
}

.loading-spinner-enhanced::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 3px solid transparent;
  border-top: 3px solid var(--primary-300);
  border-radius: 50%;
  animation: spin 2s linear infinite reverse;
}

.loading-dots-enhanced {
  display: flex;
  gap: var(--spacing-2);
}

.loading-dots-enhanced .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-500);
  animation: loading-dots-bounce 1.4s infinite ease-in-out both;
}

.loading-dots-enhanced .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots-enhanced .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots-enhanced .dot:nth-child(3) { animation-delay: 0s; }

@keyframes loading-dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Enhanced Progress Indicators */
.progress-ring {
  width: 60px;
  height: 60px;
  position: relative;
}

.progress-ring svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-ring circle {
  fill: none;
  stroke-width: 4;
  stroke-linecap: round;
}

.progress-ring .progress-ring-bg {
  stroke: var(--color-border);
}

.progress-ring .progress-ring-fill {
  stroke: var(--primary-500);
  stroke-dasharray: 157;
  stroke-dashoffset: 157;
  transition: stroke-dashoffset 0.3s ease;
}

.progress-ring .progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

/* Enhanced Notifications */
.notification-enhanced {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  max-width: 400px;
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-floating);
  backdrop-filter: blur(10px);
  z-index: 1000;
  animation: notification-slide-in 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.notification-enhanced.notification-success {
  border-left: 4px solid var(--success-500);
  background: linear-gradient(135deg, var(--success-50) 0%, var(--color-background-elevated) 100%);
}

.notification-enhanced.notification-error {
  border-left: 4px solid var(--error-500);
  background: linear-gradient(135deg, var(--error-50) 0%, var(--color-background-elevated) 100%);
}

.notification-enhanced.notification-warning {
  border-left: 4px solid var(--warning-500);
  background: linear-gradient(135deg, var(--warning-50) 0%, var(--color-background-elevated) 100%);
}

.notification-enhanced.notification-info {
  border-left: 4px solid var(--primary-500);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--color-background-elevated) 100%);
}

@keyframes notification-slide-in {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.notification-enhanced.notification-exit {
  animation: notification-slide-out 0.3s cubic-bezier(0.4, 0, 1, 1) forwards;
}

@keyframes notification-slide-out {
  from {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(100%) scale(0.95);
  }
}

/* Enhanced Drag and Drop */
.drag-drop-zone {
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  text-align: center;
  background: var(--color-background-soft);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
}

.drag-drop-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  border-radius: var(--radius-xl);
}

.drag-drop-zone.drag-over {
  border-color: var(--primary-500);
  background: var(--primary-50);
  transform: scale(1.02);
  box-shadow: var(--shadow-colored);
}

.drag-drop-zone.drag-over::before {
  opacity: 0.05;
}

.drag-drop-zone .drag-drop-content {
  position: relative;
  z-index: 1;
}

.drag-drop-zone .drag-drop-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-4);
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neutral-white);
  font-size: 24px;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.drag-drop-zone.drag-over .drag-drop-icon {
  transform: scale(1.1) rotate(8deg);
  box-shadow: var(--shadow-glow);
}

/* Enhanced Tooltips */
.tooltip-enhanced-v2 {
  position: relative;
  display: inline-block;
}

.tooltip-enhanced-v2 .tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  background: var(--neutral-900);
  color: var(--neutral-white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  pointer-events: none;
  z-index: 1000;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.tooltip-enhanced-v2 .tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--neutral-900);
}

.tooltip-enhanced-v2:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-4px);
}

/* Enhanced Modal/Dialog */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: modal-overlay-enter 0.3s ease;
}

.modal-content {
  background: var(--color-background-elevated);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-2xl);
  animation: modal-content-enter 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  border: 1px solid var(--color-border);
}

@keyframes modal-overlay-enter {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modal-content-enter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced Form Validation */
.form-field-enhanced {
  position: relative;
  margin-bottom: var(--spacing-4);
}

.form-field-enhanced .field-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  background: var(--color-surface);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  outline: none;
}

.form-field-enhanced .field-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--color-background-elevated);
}

.form-field-enhanced .field-input.error {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  animation: error-shake 0.5s ease-in-out;
}

.form-field-enhanced .field-input.success {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-field-enhanced .field-error {
  color: var(--error-600);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  animation: slideDown 0.3s ease;
}

.form-field-enhanced .field-success {
  color: var(--success-600);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  animation: slideDown 0.3s ease;
}
