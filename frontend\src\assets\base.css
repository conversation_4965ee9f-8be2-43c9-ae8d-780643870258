/* Modern Design System Color Palette - 2024 Enhanced */
:root {
  /* Primary Brand Colors - Modern Blue Gradient */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;

  /* Secondary Colors - Modern Gray Scale */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;
  --secondary-950: #020617;

  /* Extended Brand Colors - Tool Category Themes */
  --text-primary: #1e40af;
  --text-secondary: #059669;
  --dev-primary: #7c3aed;
  --dev-secondary: #a855f7;
  --file-primary: #dc2626;
  --file-secondary: #f97316;
  --calc-primary: #0891b2;
  --calc-secondary: #06b6d4;
  --media-primary: #be185d;
  --media-secondary: #ec4899;
  --edu-primary: #ca8a04;
  --edu-secondary: #eab308;

  /* Accent Colors - Modern Semantic Palette */
  --accent-purple: #8b5cf6;
  --accent-indigo: #6366f1;
  --accent-cyan: #06b6d4;
  --accent-teal: #14b8a6;
  --accent-green: #10b981;
  --accent-lime: #84cc16;
  --accent-yellow: #eab308;
  --accent-orange: #f97316;
  --accent-red: #ef4444;
  --accent-pink: #ec4899;
  --accent-rose: #f43f5e;
  --accent-emerald: #059669;

  /* Success/Warning/Error Colors */
  --success-50: #ecfdf5;
  --success-500: #10b981;
  --success-600: #059669;
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Neutral Colors */
  --neutral-white: #ffffff;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-black: #000000;

  /* Modern Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  --gradient-primary-soft: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-600) 0%, var(--secondary-800) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-purple) 0%, var(--accent-pink) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-500) 0%, var(--accent-emerald) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning-500) 0%, var(--accent-orange) 100%);
  --gradient-error: linear-gradient(135deg, var(--error-500) 0%, var(--accent-red) 100%);
  --gradient-rainbow: linear-gradient(135deg, var(--accent-purple) 0%, var(--accent-cyan) 25%, var(--accent-green) 50%, var(--accent-orange) 75%, var(--accent-pink) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);

  /* Category-specific Gradients */
  --gradient-text: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
  --gradient-dev: linear-gradient(135deg, var(--dev-primary) 0%, var(--dev-secondary) 100%);
  --gradient-file: linear-gradient(135deg, var(--file-primary) 0%, var(--file-secondary) 100%);
  --gradient-calc: linear-gradient(135deg, var(--calc-primary) 0%, var(--calc-secondary) 100%);
  --gradient-media: linear-gradient(135deg, var(--media-primary) 0%, var(--media-secondary) 100%);
  --gradient-edu: linear-gradient(135deg, var(--edu-primary) 0%, var(--edu-secondary) 100%);

  /* Advanced Gradients */
  --gradient-mesh: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                   radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                   radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  --gradient-aurora: linear-gradient(135deg,
                     rgba(59, 130, 246, 0.1) 0%,
                     rgba(139, 92, 246, 0.1) 25%,
                     rgba(16, 185, 129, 0.1) 50%,
                     rgba(245, 158, 11, 0.1) 75%,
                     rgba(236, 72, 153, 0.1) 100%);
}

/* Semantic Design Tokens */
:root {
  /* Light Theme */
  --color-background: var(--neutral-white);
  --color-background-soft: var(--neutral-50);
  --color-background-mute: var(--neutral-100);
  --color-background-elevated: var(--neutral-white);

  --color-surface: var(--neutral-white);
  --color-surface-hover: var(--neutral-50);
  --color-surface-active: var(--neutral-100);

  --color-border: var(--neutral-200);
  --color-border-hover: var(--neutral-300);
  --color-border-focus: var(--primary-500);

  --color-text-primary: var(--neutral-900);
  --color-text-secondary: var(--neutral-600);
  --color-text-tertiary: var(--neutral-500);
  --color-text-inverse: var(--neutral-white);

  --color-heading: var(--neutral-900);
  --color-link: var(--primary-600);
  --color-link-hover: var(--primary-700);

  /* Modern Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.08), 0 1px 2px -1px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -2px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -4px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 8px 10px -6px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.12);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.04);
  --shadow-colored: 0 8px 25px -5px rgba(59, 130, 246, 0.15), 0 4px 6px -4px rgba(59, 130, 246, 0.1);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.37);

  /* Enhanced Shadow Variations */
  --shadow-soft: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);
  --shadow-medium: 0 8px 16px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.04);
  --shadow-strong: 0 16px 32px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.06);
  --shadow-floating: 0 12px 24px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.06);
  --shadow-elevated: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 16px rgba(0, 0, 0, 0.08);

  /* Category-specific Colored Shadows */
  --shadow-text: 0 8px 25px -5px rgba(30, 64, 175, 0.15), 0 4px 6px -4px rgba(30, 64, 175, 0.1);
  --shadow-dev: 0 8px 25px -5px rgba(124, 58, 237, 0.15), 0 4px 6px -4px rgba(124, 58, 237, 0.1);
  --shadow-file: 0 8px 25px -5px rgba(220, 38, 38, 0.15), 0 4px 6px -4px rgba(220, 38, 38, 0.1);
  --shadow-calc: 0 8px 25px -5px rgba(8, 145, 178, 0.15), 0 4px 6px -4px rgba(8, 145, 178, 0.1);
  --shadow-media: 0 8px 25px -5px rgba(190, 24, 93, 0.15), 0 4px 6px -4px rgba(190, 24, 93, 0.1);
  --shadow-edu: 0 8px 25px -5px rgba(202, 138, 4, 0.15), 0 4px 6px -4px rgba(202, 138, 4, 0.1);

  /* Enhanced Spacing System */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0_5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1_5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2_5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3_5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;

  /* Legacy Spacing (for backward compatibility) */
  --spacing-xs: var(--spacing-1);
  --spacing-sm: var(--spacing-2);
  --spacing-md: var(--spacing-4);
  --spacing-lg: var(--spacing-6);
  --spacing-xl: var(--spacing-8);
  --spacing-2xl: var(--spacing-12);
  --spacing-3xl: var(--spacing-16);

  /* Modern Border Radius System */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-4xl: 2rem;
  --radius-full: 9999px;

  /* Modern Typography Scale */
  --font-size-2xs: 0.625rem;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  --font-size-7xl: 4.5rem;
  --font-size-8xl: 6rem;
  --font-size-9xl: 8rem;

  /* Font Weights */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
}

/* Dark Theme */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--neutral-900);
    --color-background-soft: var(--neutral-800);
    --color-background-mute: var(--neutral-700);
    --color-background-elevated: var(--neutral-800);

    --color-surface: var(--neutral-800);
    --color-surface-hover: var(--neutral-700);
    --color-surface-active: var(--neutral-600);

    --color-border: var(--neutral-700);
    --color-border-hover: var(--neutral-600);
    --color-border-focus: var(--primary-400);

    --color-text-primary: var(--neutral-100);
    --color-text-secondary: var(--neutral-300);
    --color-text-tertiary: var(--neutral-400);
    --color-text-inverse: var(--neutral-900);

    --color-heading: var(--neutral-white);
    --color-link: var(--primary-400);
    --color-link-hover: var(--primary-300);
  }
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
  color: var(--color-text-primary);
  background: var(--color-background);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: var(--line-height-normal);
  font-family:
    'Inter Variable',
    'Inter',
    'SF Pro Display',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI Variable',
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'cv11', 'ss01';
  font-variant-numeric: tabular-nums;
  overflow-x: hidden;
  letter-spacing: -0.011em;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--color-heading);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--color-link);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-link-hover);
}

/* Modern Utility Classes */
/* Text Colors */
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-inverse { color: var(--color-text-inverse); }
.text-success { color: var(--success-600); }
.text-warning { color: var(--warning-600); }
.text-error { color: var(--error-600); }

/* Background Colors */
.bg-surface { background-color: var(--color-surface); }
.bg-elevated { background-color: var(--color-background-elevated); }
.bg-soft { background-color: var(--color-background-soft); }
.bg-mute { background-color: var(--color-background-mute); }
.bg-primary { background: var(--gradient-primary); }
.bg-success { background: var(--gradient-success); }
.bg-warning { background: var(--gradient-warning); }
.bg-error { background: var(--gradient-error); }
.bg-glass { background: var(--gradient-glass); backdrop-filter: blur(10px); }

/* Shadows */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }
.shadow-colored { box-shadow: var(--shadow-colored); }
.shadow-glow { box-shadow: var(--shadow-glow); }
.shadow-glass { box-shadow: var(--shadow-glass); }
.shadow-none { box-shadow: none; }

/* Border Radius */
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-4xl { border-radius: var(--radius-4xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Interactive States */
.hover-lift:hover {
  transform: translateY(-2px);
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
  transition: box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-scale:hover {
  transform: scale(1.02);
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-blur:hover {
  backdrop-filter: blur(8px);
  transition: backdrop-filter 0.3s ease;
}

/* Focus States */
.focus-ring:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-md);
}

.focus-ring-inset:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: -2px;
}

/* Loading and Disabled States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Modern Animation Classes */
.fade-in {
  animation: fadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-up {
  animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.slide-down {
  animation: slideDown 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.slide-left {
  animation: slideLeft 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.slide-right {
  animation: slideRight 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.scale-in {
  animation: scaleIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.scale-out {
  animation: scaleOut 0.2s cubic-bezier(0.4, 0, 1, 1);
}

.bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.rotate-in {
  animation: rotateIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.blur-in {
  animation: blurIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Modern Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.92);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.92);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes blurIn {
  from {
    opacity: 0;
    filter: blur(10px);
  }
  to {
    opacity: 1;
    filter: blur(0px);
  }
}

/* Hover and Focus States */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes wiggle {
  0%, 7%, 14%, 21% {
    transform: rotate(0deg);
  }
  3.5% {
    transform: rotate(-3deg);
  }
  10.5%, 17.5% {
    transform: rotate(3deg);
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-soft);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-hover);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-tertiary);
}
