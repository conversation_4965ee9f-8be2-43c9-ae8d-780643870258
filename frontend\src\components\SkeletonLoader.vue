<template>
  <div class="skeleton-loader" :class="containerClass">
    <!-- Card Skeleton -->
    <div v-if="type === 'card'" class="skeleton-card">
      <div class="skeleton-card-header">
        <div class="skeleton skeleton-avatar"></div>
        <div class="skeleton-card-title">
          <div class="skeleton skeleton-text skeleton-text-lg" style="width: 60%;"></div>
          <div class="skeleton skeleton-text skeleton-text-sm" style="width: 40%;"></div>
        </div>
      </div>
      <div class="skeleton-card-content">
        <div class="skeleton skeleton-text" style="width: 100%;"></div>
        <div class="skeleton skeleton-text" style="width: 80%;"></div>
        <div class="skeleton skeleton-text" style="width: 90%;"></div>
      </div>
      <div class="skeleton-card-actions">
        <div class="skeleton skeleton-button"></div>
        <div class="skeleton skeleton-button" style="width: 80px;"></div>
      </div>
    </div>

    <!-- List Skeleton -->
    <div v-else-if="type === 'list'" class="skeleton-list">
      <div v-for="i in count" :key="i" class="skeleton-list-item">
        <div class="skeleton skeleton-avatar skeleton-avatar-sm"></div>
        <div class="skeleton-list-content">
          <div class="skeleton skeleton-text skeleton-text-lg" style="width: 70%;"></div>
          <div class="skeleton skeleton-text skeleton-text-sm" style="width: 50%;"></div>
        </div>
      </div>
    </div>

    <!-- Table Skeleton -->
    <div v-else-if="type === 'table'" class="skeleton-table">
      <div class="skeleton-table-header">
        <div v-for="i in columns" :key="i" class="skeleton skeleton-text skeleton-text-sm"></div>
      </div>
      <div v-for="i in rows" :key="i" class="skeleton-table-row">
        <div v-for="j in columns" :key="j" class="skeleton skeleton-text"></div>
      </div>
    </div>

    <!-- Form Skeleton -->
    <div v-else-if="type === 'form'" class="skeleton-form">
      <div v-for="i in count" :key="i" class="skeleton-form-group">
        <div class="skeleton skeleton-text skeleton-text-sm" style="width: 30%;"></div>
        <div class="skeleton skeleton-input"></div>
      </div>
      <div class="skeleton-form-actions">
        <div class="skeleton skeleton-button"></div>
        <div class="skeleton skeleton-button" style="width: 100px;"></div>
      </div>
    </div>

    <!-- Chart Skeleton -->
    <div v-else-if="type === 'chart'" class="skeleton-chart">
      <div class="skeleton-chart-header">
        <div class="skeleton skeleton-text skeleton-text-lg" style="width: 40%;"></div>
        <div class="skeleton skeleton-text skeleton-text-sm" style="width: 60%;"></div>
      </div>
      <div class="skeleton-chart-content">
        <div class="skeleton skeleton-chart-area"></div>
      </div>
      <div class="skeleton-chart-legend">
        <div v-for="i in 3" :key="i" class="skeleton-legend-item">
          <div class="skeleton skeleton-legend-color"></div>
          <div class="skeleton skeleton-text skeleton-text-sm" style="width: 80px;"></div>
        </div>
      </div>
    </div>

    <!-- Image Gallery Skeleton -->
    <div v-else-if="type === 'gallery'" class="skeleton-gallery">
      <div v-for="i in count" :key="i" class="skeleton-gallery-item">
        <div class="skeleton skeleton-image"></div>
        <div class="skeleton skeleton-text skeleton-text-sm" style="width: 80%;"></div>
      </div>
    </div>

    <!-- Custom Skeleton -->
    <div v-else-if="type === 'custom'" class="skeleton-custom">
      <slot name="skeleton"></slot>
    </div>

    <!-- Default Text Skeleton -->
    <div v-else class="skeleton-text-block">
      <div v-for="i in count" :key="i" 
           class="skeleton skeleton-text" 
           :style="{ width: getRandomWidth() }"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'card' | 'list' | 'table' | 'form' | 'chart' | 'gallery' | 'custom' | 'text'
  count?: number
  rows?: number
  columns?: number
  loading?: boolean
  containerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  count: 3,
  rows: 5,
  columns: 4,
  loading: true,
  containerClass: ''
})

const getRandomWidth = () => {
  const widths = ['100%', '90%', '80%', '70%', '85%', '95%']
  return widths[Math.floor(Math.random() * widths.length)]
}
</script>

<style scoped>
.skeleton-loader {
  width: 100%;
}

.skeleton-card {
  padding: var(--spacing-4);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  background: var(--color-background-elevated);
  margin-bottom: var(--spacing-4);
}

.skeleton-card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.skeleton-card-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.skeleton-card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.skeleton-card-actions {
  display: flex;
  gap: var(--spacing-3);
}

.skeleton-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.skeleton-list-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  background: var(--color-background-elevated);
}

.skeleton-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.skeleton-table {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--color-background-elevated);
}

.skeleton-table-header {
  display: grid;
  grid-template-columns: repeat(var(--columns, 4), 1fr);
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  background: var(--color-background-soft);
  border-bottom: 1px solid var(--color-border);
}

.skeleton-table-row {
  display: grid;
  grid-template-columns: repeat(var(--columns, 4), 1fr);
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--color-border);
}

.skeleton-table-row:last-child {
  border-bottom: none;
}

.skeleton-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.skeleton-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.skeleton-input {
  height: 40px;
  border-radius: var(--radius-lg);
}

.skeleton-form-actions {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-4);
}

.skeleton-chart {
  padding: var(--spacing-4);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  background: var(--color-background-elevated);
}

.skeleton-chart-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.skeleton-chart-content {
  margin-bottom: var(--spacing-4);
}

.skeleton-chart-area {
  height: 200px;
  border-radius: var(--radius-lg);
}

.skeleton-chart-legend {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.skeleton-legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.skeleton-legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
}

.skeleton-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.skeleton-gallery-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.skeleton-text-block {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .skeleton-card {
    padding: var(--spacing-3);
  }
  
  .skeleton-table-header,
  .skeleton-table-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-1);
  }
  
  .skeleton-gallery {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-3);
  }
  
  .skeleton-chart-legend {
    flex-direction: column;
    gap: var(--spacing-2);
  }
}

@media (max-width: 480px) {
  .skeleton-card-actions,
  .skeleton-form-actions {
    flex-direction: column;
  }
  
  .skeleton-list-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .skeleton-gallery {
    grid-template-columns: 1fr;
  }
}
</style>
