/* Enhanced UI Components and Interactions */

/* Modern Button System */
.btn-enhanced {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
  letter-spacing: 0.025em;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-enhanced:active {
  transform: scale(0.98);
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Button Variants */
.btn-primary-enhanced {
  background: var(--gradient-primary);
  color: var(--neutral-white);
  box-shadow: var(--shadow-colored);
}

.btn-primary-enhanced:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-glow);
  filter: brightness(1.1);
}

.btn-secondary-enhanced {
  background: var(--color-surface);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.btn-secondary-enhanced:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-ghost-enhanced {
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid transparent;
}

.btn-ghost-enhanced:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

/* Enhanced Card System */
.card-enhanced {
  background: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.card-enhanced:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: var(--shadow-floating);
  border-color: var(--primary-200);
}

.card-enhanced:hover::before {
  transform: scaleX(1);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: var(--shadow-elevated);
}

.card-interactive:active {
  transform: translateY(-2px) scale(1.01);
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Input System */
.input-enhanced {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.input-enhanced .input-field {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  background: var(--color-surface);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  outline: none;
}

.input-enhanced .input-field:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--color-background-elevated);
}

.input-enhanced .input-field:hover:not(:focus) {
  border-color: var(--color-border-hover);
  background: var(--color-surface-hover);
}

.input-enhanced .input-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-1);
}

.input-enhanced .input-field:focus + .input-label,
.input-enhanced .input-field:not(:placeholder-shown) + .input-label {
  color: var(--primary-600);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, 
    var(--color-background-mute) 25%, 
    var(--color-surface-hover) 50%, 
    var(--color-background-mute) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-border);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1_5) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.status-success {
  background: var(--success-50);
  color: var(--success-600);
  border: 1px solid var(--success-200);
}

.status-warning {
  background: var(--warning-50);
  color: var(--warning-600);
  border: 1px solid var(--warning-200);
}

.status-error {
  background: var(--error-50);
  color: var(--error-600);
  border: 1px solid var(--error-200);
}

.status-info {
  background: var(--primary-50);
  color: var(--primary-600);
  border: 1px solid var(--primary-200);
}

/* Enhanced Tooltips */
.tooltip-enhanced {
  position: relative;
  display: inline-block;
}

.tooltip-enhanced::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--neutral-900);
  color: var(--neutral-white);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-md);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  pointer-events: none;
  z-index: 1000;
  box-shadow: var(--shadow-lg);
}

.tooltip-enhanced::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-2px);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--neutral-900);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 1000;
}

.tooltip-enhanced:hover::before,
.tooltip-enhanced:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-4px);
}

/* Enhanced Progress Bars */
.progress-enhanced {
  width: 100%;
  height: 8px;
  background: var(--color-background-mute);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-enhanced .progress-bar {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: width 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
}

.progress-enhanced .progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
