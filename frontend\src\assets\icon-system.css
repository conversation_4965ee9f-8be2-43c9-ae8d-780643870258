/* Enhanced Icon System */

/* Icon Base Styles */
.icon-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
}

/* Icon Sizes */
.icon-xs { width: 16px; height: 16px; font-size: 12px; }
.icon-sm { width: 20px; height: 20px; font-size: 14px; }
.icon-md { width: 24px; height: 24px; font-size: 16px; }
.icon-lg { width: 32px; height: 32px; font-size: 20px; }
.icon-xl { width: 40px; height: 40px; font-size: 24px; }
.icon-2xl { width: 48px; height: 48px; font-size: 28px; }
.icon-3xl { width: 56px; height: 56px; font-size: 32px; }
.icon-4xl { width: 64px; height: 64px; font-size: 36px; }

/* Icon Containers */
.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
}

.icon-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  border-radius: inherit;
}

.icon-container:hover::before {
  width: 100%;
  height: 100%;
}

/* Icon Container Variants */
.icon-container-primary {
  background: var(--gradient-primary);
  color: var(--neutral-white);
  box-shadow: var(--shadow-colored);
}

.icon-container-primary:hover {
  transform: scale(1.1) rotate(8deg);
  box-shadow: var(--shadow-glow);
}

.icon-container-secondary {
  background: var(--color-surface);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.icon-container-secondary:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
  border-color: var(--color-border-hover);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.icon-container-ghost {
  background: transparent;
  color: var(--color-text-tertiary);
}

.icon-container-ghost:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
  transform: scale(1.05);
}

/* Category-specific Icon Containers */
.icon-container-text {
  background: var(--gradient-text);
  color: var(--neutral-white);
  box-shadow: var(--shadow-text);
}

.icon-container-text:hover {
  transform: scale(1.1) rotate(8deg);
  box-shadow: 0 0 20px rgba(30, 64, 175, 0.3);
}

.icon-container-dev {
  background: var(--gradient-dev);
  color: var(--neutral-white);
  box-shadow: var(--shadow-dev);
}

.icon-container-dev:hover {
  transform: scale(1.1) rotate(8deg);
  box-shadow: 0 0 20px rgba(124, 58, 237, 0.3);
}

.icon-container-file {
  background: var(--gradient-file);
  color: var(--neutral-white);
  box-shadow: var(--shadow-file);
}

.icon-container-file:hover {
  transform: scale(1.1) rotate(8deg);
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
}

.icon-container-calc {
  background: var(--gradient-calc);
  color: var(--neutral-white);
  box-shadow: var(--shadow-calc);
}

.icon-container-calc:hover {
  transform: scale(1.1) rotate(8deg);
  box-shadow: 0 0 20px rgba(8, 145, 178, 0.3);
}

.icon-container-media {
  background: var(--gradient-media);
  color: var(--neutral-white);
  box-shadow: var(--shadow-media);
}

.icon-container-media:hover {
  transform: scale(1.1) rotate(8deg);
  box-shadow: 0 0 20px rgba(190, 24, 93, 0.3);
}

.icon-container-edu {
  background: var(--gradient-edu);
  color: var(--neutral-white);
  box-shadow: var(--shadow-edu);
}

.icon-container-edu:hover {
  transform: scale(1.1) rotate(8deg);
  box-shadow: 0 0 20px rgba(202, 138, 4, 0.3);
}

/* Icon Animations */
.icon-spin {
  animation: icon-spin 2s linear infinite;
}

.icon-pulse {
  animation: icon-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.icon-bounce {
  animation: icon-bounce 1s infinite;
}

.icon-wiggle {
  animation: icon-wiggle 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
}

.icon-float {
  animation: icon-float 3s ease-in-out infinite;
}

@keyframes icon-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes icon-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes icon-bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -8px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes icon-wiggle {
  0%, 7%, 14%, 21% { transform: rotate(0deg); }
  3.5% { transform: rotate(-3deg); }
  10.5%, 17.5% { transform: rotate(3deg); }
}

@keyframes icon-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

/* Icon States */
.icon-loading {
  opacity: 0.6;
  animation: icon-pulse 1.5s ease-in-out infinite;
}

.icon-success {
  color: var(--success-600);
  animation: icon-bounce 0.6s ease-in-out;
}

.icon-error {
  color: var(--error-600);
  animation: icon-wiggle 0.82s ease-in-out;
}

.icon-warning {
  color: var(--warning-600);
  animation: icon-pulse 2s ease-in-out infinite;
}

/* Icon Groups */
.icon-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.icon-group .icon-enhanced {
  position: relative;
  z-index: 1;
}

.icon-group .icon-enhanced:not(:first-child) {
  margin-left: calc(var(--spacing-2) * -1);
}

.icon-group .icon-enhanced:hover {
  z-index: 2;
  transform: scale(1.1);
}

/* Icon Badges */
.icon-badge {
  position: relative;
}

.icon-badge::after {
  content: attr(data-badge);
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 16px;
  height: 16px;
  background: var(--error-500);
  color: var(--neutral-white);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: var(--shadow-sm);
  border: 2px solid var(--color-background-elevated);
}

.icon-badge[data-badge=""]::after {
  display: none;
}

/* Responsive Icon Adjustments */
@media (max-width: 768px) {
  .icon-xl { width: 36px; height: 36px; font-size: 22px; }
  .icon-2xl { width: 44px; height: 44px; font-size: 26px; }
  .icon-3xl { width: 52px; height: 52px; font-size: 30px; }
  .icon-4xl { width: 60px; height: 60px; font-size: 34px; }
}

@media (max-width: 480px) {
  .icon-lg { width: 28px; height: 28px; font-size: 18px; }
  .icon-xl { width: 32px; height: 32px; font-size: 20px; }
  .icon-2xl { width: 40px; height: 40px; font-size: 24px; }
  .icon-3xl { width: 48px; height: 48px; font-size: 28px; }
  .icon-4xl { width: 56px; height: 56px; font-size: 32px; }
}
